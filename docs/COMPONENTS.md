# 🧩 Component Documentation

Detailed documentation for all React components in the Widget Package with examples, patterns, and best practices.

## 📋 Table of Contents

1. [WidgetsProvider](#widgetsprovider)
2. [WidgetsCatalog](#widgetscatalog)
3. [Layout](#layout)
4. [WidgetErrorBoundary](#widgeterrorboundary)
5. [Component Patterns](#component-patterns)
6. [Styling Guide](#styling-guide)

## WidgetsProvider

The foundational component that provides widget context to your application.

### Basic Usage

```tsx
import { WidgetsProvider } from 'widget-package';

function App() {
  return (
    <WidgetsProvider>
      <Dashboard />
    </WidgetsProvider>
  );
}
```

### With Initial Data

```tsx
import { WidgetsProvider, createWidget } from 'widget-package';
import { CounterWidget, ChartWidget } from './widgets';

const initialWidgets = [
  createWidget('counter', 'Counter', CounterWidget, {
    category: 'Interactive',
    props: { initialValue: 0, step: 1 }
  }),
  createWidget('chart', 'Chart', ChartWidget, {
    category: 'Visualization',
    props: { type: 'line', data: [] }
  })
];

function App() {
  return (
    <WidgetsProvider initialWidgets={initialWidgets}>
      <Dashboard />
    </WidgetsProvider>
  );
}
```

### Advanced Configuration

```tsx
import { WidgetsProvider, createCategory } from 'widget-package';

const widgets = [/* your widgets */];
const categories = [
  createCategory('interactive', 'Interactive Widgets', 
    widgets.filter(w => w.category === 'Interactive')
  ),
  createCategory('charts', 'Charts & Graphs', 
    widgets.filter(w => w.category === 'Visualization')
  )
];

function App() {
  return (
    <WidgetsProvider 
      initialWidgets={widgets}
      initialCategories={categories}
    >
      <Dashboard />
    </WidgetsProvider>
  );
}
```

## WidgetsCatalog

A flexible catalog component for displaying and managing widgets.

### Basic Catalog

```tsx
import { WidgetsCatalog } from 'widget-package';

function Sidebar() {
  return (
    <div className="sidebar">
      <h2>Widget Catalog</h2>
      <WidgetsCatalog
        showSearch={true}
        showCategories={true}
        onWidgetSelect={(widget) => {
          console.log('Selected widget:', widget.name);
        }}
      />
    </div>
  );
}
```

### Draggable Catalog

```tsx
function DraggableSidebar() {
  return (
    <div className="sidebar">
      <h2>Drag Widgets to Layout</h2>
      <WidgetsCatalog
        enableDrag={true}
        showSearch={true}
        showCategories={true}
        onDrag={(widget) => {
          console.log('Dragging:', widget.name);
        }}
        onWidgetSelect={(widget) => {
          // Handle click to add widget
          addWidgetToLayout(widget);
        }}
      />
    </div>
  );
}
```

### Custom Widget Renderer

```tsx
function CustomCatalog() {
  const renderWidget = (widget: Widget) => (
    <div className="custom-widget-card">
      <div className="widget-header">
        <h4>{widget.name}</h4>
        <span className="widget-category">{widget.category}</span>
      </div>
      <p className="widget-description">{widget.description}</p>
      <div className="widget-tags">
        {widget.tags?.map(tag => (
          <span key={tag} className="tag">{tag}</span>
        ))}
      </div>
      <div className="widget-actions">
        <button onClick={() => previewWidget(widget)}>
          Preview
        </button>
        <button onClick={() => addWidget(widget)}>
          Add
        </button>
      </div>
    </div>
  );

  return (
    <WidgetsCatalog
      renderWidget={renderWidget}
      className="custom-catalog"
    />
  );
}
```

### Custom Search and Filters

```tsx
function AdvancedCatalog() {
  const renderSearch = ({ value, onChange }) => (
    <div className="search-container">
      <input
        type="text"
        value={value}
        onChange={onChange}
        placeholder="Search widgets..."
        className="search-input"
      />
      <button className="search-button">🔍</button>
    </div>
  );

  const renderCategorySelect = ({ value, options, onChange }) => (
    <div className="category-container">
      <label>Filter by category:</label>
      <select value={value} onChange={onChange} className="category-select">
        <option value="all">All Categories</option>
        {options.map(option => (
          <option key={option} value={option}>{option}</option>
        ))}
      </select>
    </div>
  );

  return (
    <WidgetsCatalog
      renderSearch={renderSearch}
      renderCategorySelect={renderCategorySelect}
      className="advanced-catalog"
    />
  );
}
```

## Layout

The Layout component provides flexible grid and flex layouts with drag & drop support.

### Grid Layout

```tsx
import { Layout } from 'widget-package';
import type { Layout as RGLLayout } from 'react-grid-layout';

function GridDashboard() {
  const [layout, setLayout] = useState<RGLLayout[]>([]);
  const [widgets, setWidgets] = useState<Widget[]>([]);

  const handleDrop = (layout: RGLLayout[], item: RGLLayout, e: Event) => {
    const dragEvent = e as DragEvent;
    const dragData = dragEvent.dataTransfer?.getData('application/json');
    
    if (dragData) {
      const data = JSON.parse(dragData);
      const newWidget = {
        ...data.widget,
        id: generateWidgetInstanceId(data.widget.id),
        layout: { x: item.x, y: item.y, w: item.w, h: item.h }
      };
      setWidgets(prev => [...prev, newWidget]);
    }
  };

  const removeWidget = (widgetId: string) => {
    setWidgets(prev => prev.filter(w => w.id !== widgetId));
    setLayout(prev => prev.filter(l => l.i !== widgetId));
  };

  return (
    <Layout
      type="grid"
      layout={layout}
      onDrop={handleDrop}
      onLayoutChange={setLayout}
      cols={12}
      rowHeight={60}
      margin={[16, 16]}
      containerPadding={[24, 24]}
      isDraggable={true}
      isResizable={true}
      isDroppable={true}
      droppingItem={{ w: 4, h: 3 }}
      layoutClassName="dashboard-grid"
      layoutStyle={{ minHeight: '600px' }}
    >
      {widgets.map(widget => {
        const WidgetComponent = widget.component;
        return (
          <div key={widget.id} data-grid={widget.layout}>
            <div className="widget-container">
              <button 
                className="widget-remove"
                onClick={() => removeWidget(widget.id)}
              >
                ×
              </button>
              <WidgetComponent {...widget.props} />
            </div>
          </div>
        );
      })}
    </Layout>
  );
}
```

### Responsive Grid Layout

```tsx
function ResponsiveDashboard() {
  const breakpoints = { lg: 1200, md: 996, sm: 768, xs: 480, xxs: 0 };
  const colsByBreakpoint = { lg: 12, md: 10, sm: 6, xs: 4, xxs: 2 };

  return (
    <Layout
      type="grid"
      layout={layout}
      onLayoutChange={setLayout}
      breakpoints={breakpoints}
      colsByBreakpoint={colsByBreakpoint}
      rowHeight={60}
      margin={[10, 10]}
      containerPadding={[10, 10]}
    >
      {/* widgets */}
    </Layout>
  );
}
```

### Flex Layout with FlexLayout-React

```tsx
import { Model, TabNode } from 'flexlayout-react';

function FlexDashboard() {
  const [flexModel, setFlexModel] = useState(() => 
    Model.fromJson({
      global: {
        tabEnableClose: true,
        tabSetEnableDrop: true,
        tabSetEnableDrag: true,
        tabSetEnableTabStrip: true,
      },
      layout: {
        type: "row",
        weight: 100,
        children: [
          {
            type: "tabset",
            weight: 50,
            children: [
              {
                type: "tab",
                name: "Welcome",
                component: "welcome",
                config: { widgetId: "welcome" }
              }
            ]
          },
          {
            type: "tabset",
            weight: 50,
            children: []
          }
        ]
      }
    })
  );

  const flexFactory = (node: TabNode) => {
    const component = node.getComponent();
    const config = node.getConfig();
    
    if (component === 'welcome') {
      return (
        <div style={{ padding: '20px' }}>
          <h2>Welcome to FlexLayout Dashboard</h2>
          <p>Drag widgets from the catalog to create new tabs.</p>
        </div>
      );
    }
    
    if (component === 'widget') {
      const widgetId = config.widgetId;
      const widget = widgets.find(w => w.id === widgetId);
      if (widget) {
        const WidgetComponent = widget.component;
        return <WidgetComponent {...widget.props} />;
      }
    }
    
    return <div>Unknown component: {component}</div>;
  };

  return (
    <Layout
      type="flex"
      flexModel={flexModel}
      flexFactory={flexFactory}
      onFlexModelChange={setFlexModel}
      layoutStyle={{ height: '600px' }}
    />
  );
}
```

### Simple CSS Flexbox Layout

```tsx
function SimpleFlexLayout() {
  return (
    <Layout
      type="flex"
      flexDirection="row"
      justifyContent="space-between"
      alignItems="stretch"
      gap="16px"
      layoutStyle={{ padding: '20px', minHeight: '400px' }}
    >
      {widgets.map(widget => {
        const WidgetComponent = widget.component;
        return (
          <div key={widget.id} style={{ flex: 1, minWidth: '300px' }}>
            <WidgetComponent {...widget.props} />
          </div>
        );
      })}
    </Layout>
  );
}
```

## WidgetErrorBoundary

Protects your layout from widget errors.

### Basic Usage

```tsx
import { WidgetErrorBoundary } from 'widget-package';

function WidgetContainer({ widget }) {
  const WidgetComponent = widget.component;
  
  return (
    <WidgetErrorBoundary widgetName={widget.name}>
      <WidgetComponent {...widget.props} />
    </WidgetErrorBoundary>
  );
}
```

### Custom Error Fallback

```tsx
function CustomErrorFallback({ error, widgetName }) {
  return (
    <div className="widget-error">
      <h4>⚠️ Widget Error</h4>
      <p>The "{widgetName}" widget encountered an error:</p>
      <details>
        <summary>Error Details</summary>
        <pre>{error.message}</pre>
      </details>
      <button onClick={() => window.location.reload()}>
        Reload Page
      </button>
    </div>
  );
}

function ProtectedWidget({ widget }) {
  return (
    <WidgetErrorBoundary 
      widgetName={widget.name}
      fallback={CustomErrorFallback}
    >
      <WidgetComponent {...widget.props} />
    </WidgetErrorBoundary>
  );
}
```

## Component Patterns

### Provider Pattern

```tsx
// App-level provider
function App() {
  return (
    <WidgetsProvider initialWidgets={widgets}>
      <Router>
        <Routes>
          <Route path="/dashboard" element={<Dashboard />} />
          <Route path="/catalog" element={<CatalogPage />} />
        </Routes>
      </Router>
    </WidgetsProvider>
  );
}

// Multiple providers for different contexts
function MultiContextApp() {
  return (
    <div>
      <WidgetsProvider initialWidgets={adminWidgets}>
        <AdminDashboard />
      </WidgetsProvider>
      
      <WidgetsProvider initialWidgets={userWidgets}>
        <UserDashboard />
      </WidgetsProvider>
    </div>
  );
}
```

### Composition Pattern

```tsx
function DashboardLayout() {
  return (
    <div className="dashboard-layout">
      <aside className="sidebar">
        <WidgetsCatalog enableDrag={true} />
      </aside>
      
      <main className="main-content">
        <Layout type="grid">
          {/* widgets */}
        </Layout>
      </main>
      
      <aside className="properties-panel">
        <WidgetProperties />
      </aside>
    </div>
  );
}
```

### Higher-Order Component Pattern

```tsx
function withWidgetProvider<T>(Component: React.ComponentType<T>) {
  return function WrappedComponent(props: T) {
    return (
      <WidgetsProvider>
        <Component {...props} />
      </WidgetsProvider>
    );
  };
}

const DashboardWithProvider = withWidgetProvider(Dashboard);
```

## Styling Guide

### CSS Classes

The components provide CSS classes for styling:

```css
/* WidgetsCatalog */
.widgets-catalog {
  /* Catalog container */
}

.widgets-catalog__search {
  /* Search input container */
}

.widgets-catalog__categories {
  /* Category filter container */
}

.widgets-catalog__grid {
  /* Widget grid container */
}

.widget-card {
  /* Individual widget card */
}

.widget-card--dragging {
  /* Widget card while being dragged */
}

/* Layout */
.layout-grid {
  /* Grid layout container */
}

.layout-flex {
  /* Flex layout container */
}

.widget-container {
  /* Individual widget container */
}

/* Error Boundary */
.widget-error {
  /* Error fallback container */
}
```

### Custom Styling Example

```css
.custom-catalog {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
}

.custom-catalog .widget-card {
  background: white;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  padding: 12px;
  margin-bottom: 8px;
  transition: all 0.2s ease;
}

.custom-catalog .widget-card:hover {
  border-color: #007bff;
  box-shadow: 0 2px 4px rgba(0,123,255,0.1);
}

.dashboard-grid {
  background: #f8f9fa;
  min-height: 600px;
}

.widget-container {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  overflow: hidden;
}

.widget-remove {
  position: absolute;
  top: 8px;
  right: 8px;
  background: #dc3545;
  color: white;
  border: none;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  cursor: pointer;
  z-index: 1000;
}
```

---

This component documentation provides detailed examples and patterns for using each component effectively. For more advanced usage and integration patterns, see the [API Reference](API.md) and [Integration Guide](../INTEGRATION_GUIDE.md).
