# 📚 Widget Package API Reference

Complete API documentation for all components, hooks, types, and utilities in the Widget Package.

## 📋 Table of Contents

1. [Components](#components)
   - [WidgetsProvider](#widgetsprovider)
   - [WidgetsCatalog](#widgetscatalog)
   - [Layout](#layout)
   - [WidgetErrorBoundary](#widgeterrorboundary)
2. [Hooks](#hooks)
   - [useWidgets](#usewidgets)
3. [Types](#types)
4. [Utilities](#utilities)
5. [Examples](#examples)

## Components

### WidgetsProvider

The main context provider that manages widget state and provides context to child components.

#### Props

```tsx
interface WidgetsProviderProps {
  children: React.ReactNode;
  initialWidgets?: Widget[];
  initialCategories?: WidgetCategory[];
}
```

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `children` | `React.ReactNode` | **Required** | Child components that will have access to the widgets context |
| `initialWidgets` | `Widget[]` | `[]` | Array of widgets to initialize the provider with |
| `initialCategories` | `WidgetCategory[]` | `[]` | Array of widget categories to initialize the provider with |

#### Usage

```tsx
import { WidgetsProvider } from 'widget-package';

const widgets = [
  {
    id: 'counter',
    name: 'Counter Widget',
    component: CounterWidget,
    category: 'Interactive',
    props: { initialValue: 0 }
  }
];

function App() {
  return (
    <WidgetsProvider initialWidgets={widgets}>
      <Dashboard />
    </WidgetsProvider>
  );
}
```

#### Context Value

The provider exposes the following context value through the `useWidgets` hook:

```tsx
interface WidgetsContextType {
  widgets: Widget[];
  categories: WidgetCategory[];
  addWidget: (widget: Widget) => void;
  removeWidget: (widgetId: string) => void;
  getWidgetsByCategory: (categoryId: string) => Widget[];
  searchWidgets: (query: string) => Widget[];
}
```

### WidgetsCatalog

A component that displays widgets in a searchable, filterable catalog with drag support.

#### Props

```tsx
interface WidgetsCatalogProps {
  className?: string;
  showSearch?: boolean;
  showCategories?: boolean;
  widgets?: Widget[];
  onWidgetSelect?: (widget: Widget) => void;
  onDrag?: (widget: Widget) => void;
  renderWidget?: (widget: Widget) => React.ReactNode;
  enableDrag?: boolean;
  renderSearch?: (props: SearchProps) => React.ReactNode;
  renderCategorySelect?: (props: CategoryProps) => React.ReactNode;
}
```

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `className` | `string` | `''` | Additional CSS class for the catalog container |
| `showSearch` | `boolean` | `true` | Whether to show the search input |
| `showCategories` | `boolean` | `true` | Whether to show the category filter |
| `widgets` | `Widget[]` | Context widgets | Override the widgets from context |
| `onWidgetSelect` | `(widget: Widget) => void` | `undefined` | Callback when a widget is selected/clicked |
| `onDrag` | `(widget: Widget) => void` | `undefined` | Callback when a widget drag starts |
| `renderWidget` | `(widget: Widget) => React.ReactNode` | Default renderer | Custom widget card renderer |
| `enableDrag` | `boolean` | `false` | Enable drag and drop functionality |
| `renderSearch` | `(props: SearchProps) => React.ReactNode` | Default input | Custom search input renderer |
| `renderCategorySelect` | `(props: CategoryProps) => React.ReactNode` | Default select | Custom category select renderer |

#### Custom Renderers

##### renderWidget

```tsx
const customRenderWidget = (widget: Widget) => (
  <div className="custom-widget-card">
    <h4>{widget.name}</h4>
    <p>{widget.description}</p>
    <div className="widget-tags">
      {widget.tags?.map(tag => (
        <span key={tag} className="tag">{tag}</span>
      ))}
    </div>
  </div>
);

<WidgetsCatalog renderWidget={customRenderWidget} />
```

##### renderSearch

```tsx
const customRenderSearch = ({ value, onChange }) => (
  <input
    type="text"
    value={value}
    onChange={onChange}
    placeholder="Search widgets..."
    className="custom-search-input"
  />
);

<WidgetsCatalog renderSearch={customRenderSearch} />
```

#### Usage Examples

**Basic Catalog:**
```tsx
<WidgetsCatalog
  showSearch={true}
  showCategories={true}
  onWidgetSelect={(widget) => console.log('Selected:', widget.name)}
/>
```

**Draggable Catalog:**
```tsx
<WidgetsCatalog
  enableDrag={true}
  onDrag={(widget) => console.log('Dragging:', widget.name)}
  onWidgetSelect={(widget) => addToLayout(widget)}
/>
```

**Custom Styled Catalog:**
```tsx
<WidgetsCatalog
  className="my-custom-catalog"
  renderWidget={(widget) => (
    <CustomWidgetCard widget={widget} />
  )}
  renderSearch={({ value, onChange }) => (
    <CustomSearchInput value={value} onChange={onChange} />
  )}
/>
```

### Layout

A flexible layout component that supports both grid (react-grid-layout) and flex (flexlayout-react) layouts with drag & drop.

#### Props

```tsx
interface LayoutProps {
  type: 'grid' | 'flex';
  children?: React.ReactNode;
  className?: string;
  
  // Grid layout props (react-grid-layout)
  onDrop?: (layout: Layout[], item: Layout, e: Event) => void;
  onLayoutChange?: (layout: Layout[]) => void;
  layout?: Layout[];
  cols?: number;
  rowHeight?: number;
  margin?: [number, number];
  containerPadding?: [number, number];
  isDraggable?: boolean;
  isResizable?: boolean;
  isDroppable?: boolean;
  droppingItem?: Partial<Layout>;
  breakpoints?: { [key: string]: number };
  colsByBreakpoint?: { [key: string]: number };
  layoutClassName?: string;
  layoutStyle?: React.CSSProperties;
  
  // Flex layout props (flexlayout-react)
  flexModel?: Model | null;
  flexFactory?: (node: TabNode) => React.ReactNode;
  onFlexModelChange?: (model: Model) => void;
  droppingItemFlex?: Partial<Layout>;
  
  // Simple flex layout props (CSS flexbox fallback)
  flexDirection?: 'row' | 'column' | 'row-reverse' | 'column-reverse';
  justifyContent?: 'flex-start' | 'flex-end' | 'center' | 'space-between' | 'space-around' | 'space-evenly';
  alignItems?: 'stretch' | 'flex-start' | 'flex-end' | 'center' | 'baseline';
  flexWrap?: 'nowrap' | 'wrap' | 'wrap-reverse';
  gap?: number | string;
}
```

#### Grid Layout Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `type` | `'grid'` | **Required** | Layout type |
| `layout` | `Layout[]` | `[]` | Array of layout items |
| `onLayoutChange` | `(layout: Layout[]) => void` | `undefined` | Callback when layout changes |
| `onDrop` | `(layout: Layout[], item: Layout, e: Event) => void` | `undefined` | Callback when item is dropped |
| `cols` | `number` | `12` | Number of columns in the grid |
| `rowHeight` | `number` | `30` | Height of each row in pixels |
| `margin` | `[number, number]` | `[10, 10]` | Margin between items [x, y] |
| `containerPadding` | `[number, number]` | `[10, 10]` | Padding around the container [x, y] |
| `isDraggable` | `boolean` | `true` | Whether items can be dragged |
| `isResizable` | `boolean` | `true` | Whether items can be resized |
| `isDroppable` | `boolean` | `true` | Whether new items can be dropped |
| `droppingItem` | `Partial<Layout>` | `{ w: 4, h: 5 }` | Default size for dropped items |

#### Flex Layout Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `type` | `'flex'` | **Required** | Layout type |
| `flexModel` | `Model \| null` | `null` | FlexLayout model |
| `flexFactory` | `(node: TabNode) => React.ReactNode` | `undefined` | Factory function for rendering tabs |
| `onFlexModelChange` | `(model: Model) => void` | `undefined` | Callback when model changes |
| `droppingItemFlex` | `Partial<Layout>` | `{ w: 2, h: 2 }` | Default size for dropped items in flex layout |

#### Usage Examples

**Grid Layout:**
```tsx
import { Layout } from 'widget-package';
import type { Layout as RGLLayout } from 'react-grid-layout';

const [layout, setLayout] = useState<RGLLayout[]>([]);
const [widgets, setWidgets] = useState<Widget[]>([]);

const handleDrop = (layout: RGLLayout[], item: RGLLayout, e: Event) => {
  const dragEvent = e as DragEvent;
  const dragData = dragEvent.dataTransfer?.getData('application/json');
  
  if (dragData) {
    const data = JSON.parse(dragData);
    const newWidget = {
      ...data.widget,
      id: `${data.widget.id}-${Date.now()}`,
      layout: { x: item.x, y: item.y, w: item.w, h: item.h }
    };
    setWidgets(prev => [...prev, newWidget]);
  }
};

<Layout
  type="grid"
  layout={layout}
  onDrop={handleDrop}
  onLayoutChange={setLayout}
  cols={12}
  rowHeight={60}
  isDraggable={true}
  isResizable={true}
  isDroppable={true}
>
  {widgets.map(widget => {
    const WidgetComponent = widget.component;
    return (
      <div key={widget.id} data-grid={widget.layout}>
        <WidgetComponent {...widget.props} />
      </div>
    );
  })}
</Layout>
```

**Flex Layout:**
```tsx
import { Model, TabNode } from 'flexlayout-react';

const flexModel = Model.fromJson({
  global: {
    tabEnableClose: true,
    tabSetEnableDrop: true,
    tabSetEnableDrag: true,
  },
  layout: {
    type: "row",
    weight: 100,
    children: [{
      type: "tabset",
      weight: 100,
      children: []
    }]
  }
});

const flexFactory = (node: TabNode) => {
  const component = node.getComponent();
  if (component === 'widget') {
    const widgetId = node.getConfig().widgetId;
    const widget = widgets.find(w => w.id === widgetId);
    if (widget) {
      const WidgetComponent = widget.component;
      return <WidgetComponent {...widget.props} />;
    }
  }
  return <div>Unknown component</div>;
};

<Layout
  type="flex"
  flexModel={flexModel}
  flexFactory={flexFactory}
  onFlexModelChange={setFlexModel}
/>
```

### WidgetErrorBoundary

An error boundary component that wraps widgets to prevent a single widget error from breaking the entire layout.

#### Props

```tsx
interface WidgetErrorBoundaryProps {
  children: React.ReactNode;
  widgetName?: string;
  fallback?: React.ComponentType<{ error: Error; widgetName?: string }>;
}
```

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `children` | `React.ReactNode` | **Required** | The widget component to wrap |
| `widgetName` | `string` | `'Widget'` | Name of the widget for error reporting |
| `fallback` | `React.ComponentType` | Default error UI | Custom error fallback component |

#### Usage

```tsx
<WidgetErrorBoundary widgetName="Counter Widget">
  <CounterWidget />
</WidgetErrorBoundary>
```

## Hooks

### useWidgets

A hook that provides access to the widgets context. Must be used within a `WidgetsProvider`.

#### Returns

```tsx
interface WidgetsContextType {
  widgets: Widget[];
  categories: WidgetCategory[];
  addWidget: (widget: Widget) => void;
  removeWidget: (widgetId: string) => void;
  getWidgetsByCategory: (categoryId: string) => Widget[];
  searchWidgets: (query: string) => Widget[];
}
```

#### Usage

```tsx
import { useWidgets } from 'widget-package';

function MyComponent() {
  const {
    widgets,
    categories,
    addWidget,
    removeWidget,
    getWidgetsByCategory,
    searchWidgets
  } = useWidgets();

  const handleAddWidget = () => {
    addWidget({
      id: 'new-widget',
      name: 'New Widget',
      component: MyWidget,
      category: 'Custom'
    });
  };

  const interactiveWidgets = getWidgetsByCategory('Interactive');
  const searchResults = searchWidgets('counter');

  return (
    <div>
      <p>Total widgets: {widgets.length}</p>
      <button onClick={handleAddWidget}>Add Widget</button>
    </div>
  );
}
```

## Types

### Widget

```tsx
interface Widget {
  id: string;                    // Unique identifier
  name: string;                  // Display name
  description?: string;          // Optional description
  component: React.ComponentType<any>; // React component
  props?: Record<string, any>;   // Default props for the component
  category?: string;             // Category for grouping
  tags?: string[];              // Tags for searching
  layout?: {                    // Layout properties for grid layout
    x?: number;                 // X position
    y?: number;                 // Y position
    w?: number;                 // Width in grid units
    h?: number;                 // Height in grid units
    minW?: number;              // Minimum width
    minH?: number;              // Minimum height
    maxW?: number;              // Maximum width
    maxH?: number;              // Maximum height
    static?: boolean;           // Whether item is static (non-draggable/resizable)
    isDraggable?: boolean;      // Override draggable setting
    isResizable?: boolean;      // Override resizable setting
  };
}
```

### WidgetCategory

```tsx
interface WidgetCategory {
  id: string;                   // Unique identifier
  name: string;                 // Display name
  description?: string;         // Optional description
  widgets: Widget[];            // Widgets in this category
}
```

### DragItem

```tsx
interface DragItem {
  widget: Widget;               // The widget being dragged
  type: string;                 // Drag type identifier
}
```

## Utilities

For detailed utility function documentation, see [docs/UTILITIES.md](UTILITIES.md).

### Quick Reference

- `createWidget()` - Create a widget with defaults
- `createCategory()` - Create a widget category
- `generateWidgetInstanceId()` - Generate unique IDs
- `validateWidget()` - Validate widget configuration
- `widgetToGridLayout()` - Convert widget to grid layout
- `findOptimalPosition()` - Find optimal grid position
- `WidgetRegistry` - Manage widget components for serialization
- `serializeWidget()` / `deserializeWidget()` - Widget serialization

## Examples

For complete examples and integration patterns, see:

- [Integration Guide](../INTEGRATION_GUIDE.md)
- [Examples Directory](../examples/)
- [Component Examples](COMPONENTS.md)
- [Hook Examples](HOOKS.md)

---

This API reference provides comprehensive documentation for all public APIs in the Widget Package. For implementation details and advanced usage patterns, refer to the linked documentation and examples.
