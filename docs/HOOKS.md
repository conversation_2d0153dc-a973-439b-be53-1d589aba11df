# 🪝 Hooks Documentation

Comprehensive guide to using hooks in the Widget Package for state management and widget operations.

## 📋 Table of Contents

1. [useWidgets](#usewidgets)
2. [Hook Patterns](#hook-patterns)
3. [Custom Hooks](#custom-hooks)
4. [Best Practices](#best-practices)
5. [Examples](#examples)

## useWidgets

The primary hook for accessing widget context and performing widget operations.

### Basic Usage

```tsx
import { useWidgets } from 'widget-package';

function MyComponent() {
  const {
    widgets,
    categories,
    addWidget,
    removeWidget,
    getWidgetsByCategory,
    searchWidgets
  } = useWidgets();

  return (
    <div>
      <p>Total widgets: {widgets.length}</p>
      <p>Categories: {categories.length}</p>
    </div>
  );
}
```

### Return Value

```tsx
interface WidgetsContextType {
  widgets: Widget[];                                    // All available widgets
  categories: WidgetCategory[];                         // All widget categories
  addWidget: (widget: Widget) => void;                 // Add a new widget
  removeWidget: (widgetId: string) => void;            // Remove a widget by ID
  getWidgetsByCategory: (categoryId: string) => Widget[]; // Get widgets in category
  searchWidgets: (query: string) => Widget[];          // Search widgets by query
}
```

### Widget Management

#### Adding Widgets

```tsx
function AddWidgetButton() {
  const { addWidget } = useWidgets();

  const handleAddWidget = () => {
    const newWidget = {
      id: `widget-${Date.now()}`,
      name: 'New Widget',
      component: MyWidgetComponent,
      category: 'Custom',
      description: 'A dynamically added widget',
      props: { title: 'Hello World' }
    };

    addWidget(newWidget);
  };

  return (
    <button onClick={handleAddWidget}>
      Add New Widget
    </button>
  );
}
```

#### Removing Widgets

```tsx
function WidgetList() {
  const { widgets, removeWidget } = useWidgets();

  return (
    <div>
      {widgets.map(widget => (
        <div key={widget.id} className="widget-item">
          <span>{widget.name}</span>
          <button onClick={() => removeWidget(widget.id)}>
            Remove
          </button>
        </div>
      ))}
    </div>
  );
}
```

### Category Operations

#### Filtering by Category

```tsx
function CategoryFilter() {
  const { categories, getWidgetsByCategory } = useWidgets();
  const [selectedCategory, setSelectedCategory] = useState('all');

  const filteredWidgets = selectedCategory === 'all' 
    ? widgets 
    : getWidgetsByCategory(selectedCategory);

  return (
    <div>
      <select 
        value={selectedCategory} 
        onChange={(e) => setSelectedCategory(e.target.value)}
      >
        <option value="all">All Categories</option>
        {categories.map(category => (
          <option key={category.id} value={category.id}>
            {category.name}
          </option>
        ))}
      </select>

      <div className="widget-grid">
        {filteredWidgets.map(widget => (
          <WidgetCard key={widget.id} widget={widget} />
        ))}
      </div>
    </div>
  );
}
```

#### Category Statistics

```tsx
function CategoryStats() {
  const { widgets, categories, getWidgetsByCategory } = useWidgets();

  const categoryStats = categories.map(category => ({
    ...category,
    count: getWidgetsByCategory(category.id).length
  }));

  return (
    <div className="category-stats">
      <h3>Widget Distribution</h3>
      {categoryStats.map(stat => (
        <div key={stat.id} className="stat-item">
          <span>{stat.name}</span>
          <span className="count">{stat.count}</span>
        </div>
      ))}
      <div className="total">
        Total: {widgets.length} widgets
      </div>
    </div>
  );
}
```

### Search Operations

#### Basic Search

```tsx
function SearchableWidgetList() {
  const { searchWidgets } = useWidgets();
  const [query, setQuery] = useState('');

  const searchResults = searchWidgets(query);

  return (
    <div>
      <input
        type="text"
        value={query}
        onChange={(e) => setQuery(e.target.value)}
        placeholder="Search widgets..."
      />
      
      <div className="search-results">
        {searchResults.map(widget => (
          <WidgetCard key={widget.id} widget={widget} />
        ))}
      </div>
      
      {query && (
        <p>{searchResults.length} results for "{query}"</p>
      )}
    </div>
  );
}
```

#### Advanced Search with Filters

```tsx
function AdvancedSearch() {
  const { widgets, searchWidgets, getWidgetsByCategory } = useWidgets();
  const [query, setQuery] = useState('');
  const [category, setCategory] = useState('all');

  const searchResults = useMemo(() => {
    let results = searchWidgets(query);
    
    if (category !== 'all') {
      results = results.filter(widget => widget.category === category);
    }
    
    return results;
  }, [query, category, searchWidgets]);

  return (
    <div className="advanced-search">
      <div className="search-controls">
        <input
          type="text"
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          placeholder="Search widgets..."
        />
        
        <select 
          value={category} 
          onChange={(e) => setCategory(e.target.value)}
        >
          <option value="all">All Categories</option>
          {/* category options */}
        </select>
      </div>
      
      <div className="results">
        {searchResults.map(widget => (
          <WidgetCard key={widget.id} widget={widget} />
        ))}
      </div>
    </div>
  );
}
```

## Hook Patterns

### Conditional Hook Usage

```tsx
function ConditionalWidgetManager({ enabled }: { enabled: boolean }) {
  // Only use the hook when enabled
  const widgetContext = enabled ? useWidgets() : null;

  if (!enabled) {
    return <div>Widget management disabled</div>;
  }

  const { widgets, addWidget } = widgetContext!;

  return (
    <div>
      <p>Managing {widgets.length} widgets</p>
      {/* widget management UI */}
    </div>
  );
}
```

### Hook with Error Handling

```tsx
function SafeWidgetManager() {
  const [error, setError] = useState<string | null>(null);

  try {
    const { widgets, addWidget, removeWidget } = useWidgets();

    const safeAddWidget = (widget: Widget) => {
      try {
        addWidget(widget);
        setError(null);
      } catch (err) {
        setError(`Failed to add widget: ${err.message}`);
      }
    };

    const safeRemoveWidget = (widgetId: string) => {
      try {
        removeWidget(widgetId);
        setError(null);
      } catch (err) {
        setError(`Failed to remove widget: ${err.message}`);
      }
    };

    return (
      <div>
        {error && <div className="error">{error}</div>}
        {/* widget management UI */}
      </div>
    );
  } catch (err) {
    return (
      <div className="error">
        Widget context not available. Make sure you're inside a WidgetsProvider.
      </div>
    );
  }
}
```

## Custom Hooks

### useWidgetFilter

```tsx
function useWidgetFilter() {
  const { widgets, searchWidgets, getWidgetsByCategory } = useWidgets();
  const [filters, setFilters] = useState({
    query: '',
    category: 'all',
    tags: [] as string[]
  });

  const filteredWidgets = useMemo(() => {
    let results = widgets;

    // Apply search query
    if (filters.query) {
      results = searchWidgets(filters.query);
    }

    // Apply category filter
    if (filters.category !== 'all') {
      results = results.filter(widget => widget.category === filters.category);
    }

    // Apply tag filters
    if (filters.tags.length > 0) {
      results = results.filter(widget =>
        filters.tags.every(tag =>
          widget.tags?.includes(tag)
        )
      );
    }

    return results;
  }, [widgets, filters, searchWidgets]);

  const updateFilter = (key: keyof typeof filters, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  const clearFilters = () => {
    setFilters({ query: '', category: 'all', tags: [] });
  };

  return {
    filteredWidgets,
    filters,
    updateFilter,
    clearFilters,
    hasActiveFilters: filters.query || filters.category !== 'all' || filters.tags.length > 0
  };
}

// Usage
function FilteredWidgetList() {
  const { filteredWidgets, filters, updateFilter, clearFilters, hasActiveFilters } = useWidgetFilter();

  return (
    <div>
      <div className="filters">
        <input
          value={filters.query}
          onChange={(e) => updateFilter('query', e.target.value)}
          placeholder="Search..."
        />
        {hasActiveFilters && (
          <button onClick={clearFilters}>Clear Filters</button>
        )}
      </div>
      
      <div className="results">
        {filteredWidgets.map(widget => (
          <WidgetCard key={widget.id} widget={widget} />
        ))}
      </div>
    </div>
  );
}
```

### useWidgetSelection

```tsx
function useWidgetSelection() {
  const [selectedWidgets, setSelectedWidgets] = useState<Set<string>>(new Set());

  const selectWidget = (widgetId: string) => {
    setSelectedWidgets(prev => new Set(prev).add(widgetId));
  };

  const deselectWidget = (widgetId: string) => {
    setSelectedWidgets(prev => {
      const newSet = new Set(prev);
      newSet.delete(widgetId);
      return newSet;
    });
  };

  const toggleWidget = (widgetId: string) => {
    if (selectedWidgets.has(widgetId)) {
      deselectWidget(widgetId);
    } else {
      selectWidget(widgetId);
    }
  };

  const selectAll = (widgetIds: string[]) => {
    setSelectedWidgets(new Set(widgetIds));
  };

  const clearSelection = () => {
    setSelectedWidgets(new Set());
  };

  const isSelected = (widgetId: string) => selectedWidgets.has(widgetId);

  return {
    selectedWidgets: Array.from(selectedWidgets),
    selectWidget,
    deselectWidget,
    toggleWidget,
    selectAll,
    clearSelection,
    isSelected,
    selectedCount: selectedWidgets.size
  };
}

// Usage
function SelectableWidgetList() {
  const { widgets } = useWidgets();
  const {
    selectedWidgets,
    toggleWidget,
    selectAll,
    clearSelection,
    isSelected,
    selectedCount
  } = useWidgetSelection();

  return (
    <div>
      <div className="selection-controls">
        <button onClick={() => selectAll(widgets.map(w => w.id))}>
          Select All
        </button>
        <button onClick={clearSelection}>
          Clear Selection
        </button>
        <span>Selected: {selectedCount}</span>
      </div>
      
      <div className="widget-list">
        {widgets.map(widget => (
          <div
            key={widget.id}
            className={`widget-item ${isSelected(widget.id) ? 'selected' : ''}`}
            onClick={() => toggleWidget(widget.id)}
          >
            {widget.name}
          </div>
        ))}
      </div>
    </div>
  );
}
```

### useWidgetPersistence

```tsx
function useWidgetPersistence(storageKey: string = 'widgets') {
  const { widgets, addWidget, removeWidget } = useWidgets();

  // Save widgets to localStorage
  const saveWidgets = useCallback(() => {
    try {
      const serializedWidgets = widgets.map(widget => ({
        ...widget,
        component: widget.component.name // Store component name instead of function
      }));
      localStorage.setItem(storageKey, JSON.stringify(serializedWidgets));
    } catch (error) {
      console.error('Failed to save widgets:', error);
    }
  }, [widgets, storageKey]);

  // Load widgets from localStorage
  const loadWidgets = useCallback(() => {
    try {
      const saved = localStorage.getItem(storageKey);
      if (saved) {
        const parsedWidgets = JSON.parse(saved);
        // Note: You'd need a component registry to restore the actual components
        return parsedWidgets;
      }
    } catch (error) {
      console.error('Failed to load widgets:', error);
    }
    return [];
  }, [storageKey]);

  // Auto-save when widgets change
  useEffect(() => {
    saveWidgets();
  }, [widgets, saveWidgets]);

  return {
    saveWidgets,
    loadWidgets
  };
}
```

## Best Practices

### 1. Always Use Inside Provider

```tsx
// ❌ Wrong - will throw error
function BadComponent() {
  const { widgets } = useWidgets(); // Error: not inside provider
  return <div>{widgets.length}</div>;
}

// ✅ Correct - inside provider
function App() {
  return (
    <WidgetsProvider>
      <GoodComponent />
    </WidgetsProvider>
  );
}

function GoodComponent() {
  const { widgets } = useWidgets(); // Works correctly
  return <div>{widgets.length}</div>;
}
```

### 2. Memoize Expensive Operations

```tsx
function OptimizedComponent() {
  const { widgets, searchWidgets } = useWidgets();
  const [query, setQuery] = useState('');

  // ✅ Memoize expensive search operations
  const searchResults = useMemo(() => {
    return searchWidgets(query);
  }, [query, searchWidgets]);

  // ✅ Memoize derived data
  const widgetsByCategory = useMemo(() => {
    return widgets.reduce((acc, widget) => {
      const category = widget.category || 'Uncategorized';
      if (!acc[category]) acc[category] = [];
      acc[category].push(widget);
      return acc;
    }, {} as Record<string, Widget[]>);
  }, [widgets]);

  return (
    <div>
      {/* Use memoized results */}
    </div>
  );
}
```

### 3. Handle Loading States

```tsx
function LoadingAwareComponent() {
  const { widgets } = useWidgets();
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Simulate loading
    const timer = setTimeout(() => setIsLoading(false), 1000);
    return () => clearTimeout(timer);
  }, []);

  if (isLoading) {
    return <div>Loading widgets...</div>;
  }

  if (widgets.length === 0) {
    return <div>No widgets available</div>;
  }

  return (
    <div>
      {widgets.map(widget => (
        <WidgetCard key={widget.id} widget={widget} />
      ))}
    </div>
  );
}
```

### 4. Error Boundaries with Hooks

```tsx
function SafeHookUsage() {
  const [error, setError] = useState<string | null>(null);

  try {
    const { widgets, addWidget } = useWidgets();

    const handleAddWidget = async (widget: Widget) => {
      try {
        addWidget(widget);
        setError(null);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Unknown error');
      }
    };

    return (
      <div>
        {error && <div className="error">{error}</div>}
        {/* component content */}
      </div>
    );
  } catch (err) {
    return <div>Error: Widget context not available</div>;
  }
}
```

## Examples

### Complete Widget Manager

```tsx
function WidgetManager() {
  const {
    widgets,
    categories,
    addWidget,
    removeWidget,
    searchWidgets
  } = useWidgets();

  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');

  const filteredWidgets = useMemo(() => {
    let results = searchQuery ? searchWidgets(searchQuery) : widgets;
    
    if (selectedCategory !== 'all') {
      results = results.filter(w => w.category === selectedCategory);
    }
    
    return results;
  }, [widgets, searchQuery, selectedCategory, searchWidgets]);

  return (
    <div className="widget-manager">
      <div className="controls">
        <input
          type="text"
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          placeholder="Search widgets..."
        />
        
        <select
          value={selectedCategory}
          onChange={(e) => setSelectedCategory(e.target.value)}
        >
          <option value="all">All Categories</option>
          {categories.map(cat => (
            <option key={cat.id} value={cat.id}>{cat.name}</option>
          ))}
        </select>
      </div>

      <div className="widget-grid">
        {filteredWidgets.map(widget => (
          <div key={widget.id} className="widget-card">
            <h4>{widget.name}</h4>
            <p>{widget.description}</p>
            <button onClick={() => removeWidget(widget.id)}>
              Remove
            </button>
          </div>
        ))}
      </div>

      <div className="stats">
        <p>Showing {filteredWidgets.length} of {widgets.length} widgets</p>
      </div>
    </div>
  );
}
```

---

This hooks documentation provides comprehensive examples and patterns for effectively using the Widget Package hooks. For more information, see the [API Reference](API.md) and [Component Documentation](COMPONENTS.md).
