# 🛠️ Utilities Documentation

Comprehensive guide to utility functions in the Widget Package for widget creation, layout management, and serialization.

## 📋 Table of Contents

1. [Widget Creation](#widget-creation)
2. [Layout Utilities](#layout-utilities)
3. [Serialization](#serialization)
4. [Validation](#validation)
5. [Helper Functions](#helper-functions)
6. [Examples](#examples)

## Widget Creation

### createWidget

Creates a widget with sensible defaults and type safety.

```tsx
function createWidget(
  id: string,
  name: string,
  component: React.ComponentType<any>,
  options?: Partial<Omit<Widget, 'id' | 'name' | 'component'>>
): Widget
```

#### Parameters

| Parameter | Type | Description |
|-----------|------|-------------|
| `id` | `string` | Unique identifier for the widget |
| `name` | `string` | Display name for the widget |
| `component` | `React.ComponentType<any>` | React component to render |
| `options` | `Partial<Widget>` | Additional widget properties |

#### Examples

**Basic Widget:**
```tsx
import { createWidget } from 'widget-package';
import { CounterWidget } from './widgets/CounterWidget';

const counterWidget = createWidget(
  'counter',
  'Counter Widget',
  CounterWidget
);

// Result:
// {
//   id: 'counter',
//   name: 'Counter Widget',
//   component: CounterWidget,
//   description: 'Counter Widget widget',
//   category: 'General',
//   tags: ['counter'],
//   props: {},
//   layout: { w: 4, h: 3, x: 0, y: 0, minW: 2, minH: 2 }
// }
```

**Widget with Options:**
```tsx
const chartWidget = createWidget(
  'line-chart',
  'Line Chart',
  LineChartWidget,
  {
    description: 'Interactive line chart for data visualization',
    category: 'Charts',
    tags: ['chart', 'visualization', 'data'],
    props: {
      type: 'line',
      data: [],
      showLegend: true
    },
    layout: {
      w: 6,
      h: 4,
      minW: 4,
      minH: 3,
      maxW: 12,
      maxH: 8
    }
  }
);
```

**Widget with Custom Layout:**
```tsx
const dashboardWidget = createWidget(
  'dashboard-summary',
  'Dashboard Summary',
  SummaryWidget,
  {
    category: 'Analytics',
    layout: {
      w: 12,  // Full width
      h: 2,   // Short height
      static: true,  // Cannot be moved/resized
      isDraggable: false,
      isResizable: false
    }
  }
);
```

### createCategory

Creates a widget category with associated widgets.

```tsx
function createCategory(
  id: string,
  name: string,
  widgets: Widget[],
  description?: string
): WidgetCategory
```

#### Examples

```tsx
import { createCategory } from 'widget-package';

const chartWidgets = [lineChartWidget, barChartWidget, pieChartWidget];

const chartsCategory = createCategory(
  'charts',
  'Charts & Graphs',
  chartWidgets,
  'Data visualization widgets for charts and graphs'
);

// Multiple categories
const categories = [
  createCategory('interactive', 'Interactive', [counterWidget, formWidget]),
  createCategory('charts', 'Charts', chartWidgets),
  createCategory('analytics', 'Analytics', [summaryWidget, metricsWidget])
];
```

## Layout Utilities

### widgetToGridLayout

Converts a widget to react-grid-layout format.

```tsx
function widgetToGridLayout(widget: Widget): Layout
```

#### Example

```tsx
import { widgetToGridLayout } from 'widget-package';

const widget = createWidget('test', 'Test Widget', TestComponent, {
  layout: { w: 4, h: 3, x: 2, y: 1 }
});

const gridLayout = widgetToGridLayout(widget);
// Result: { i: 'test', w: 4, h: 3, x: 2, y: 1, minW: 2, minH: 2 }
```

### gridLayoutToWidget

Converts react-grid-layout format back to widget layout.

```tsx
function gridLayoutToWidget(layout: Layout): Widget['layout']
```

#### Example

```tsx
import { gridLayoutToWidget } from 'widget-package';

const gridLayout = { i: 'test', w: 6, h: 4, x: 0, y: 0 };
const widgetLayout = gridLayoutToWidget(gridLayout);
// Result: { w: 6, h: 4, x: 0, y: 0 }
```

### findOptimalPosition

Finds the optimal position for a new widget in the grid.

```tsx
function findOptimalPosition(
  existingLayouts: Layout[],
  newWidget: Widget,
  cols?: number
): { x: number; y: number }
```

#### Example

```tsx
import { findOptimalPosition } from 'widget-package';

const existingLayouts = [
  { i: '1', w: 4, h: 3, x: 0, y: 0 },
  { i: '2', w: 4, h: 3, x: 4, y: 0 },
  { i: '3', w: 4, h: 3, x: 8, y: 0 }
];

const newWidget = createWidget('new', 'New Widget', Component, {
  layout: { w: 4, h: 3 }
});

const position = findOptimalPosition(existingLayouts, newWidget, 12);
// Result: { x: 0, y: 3 } - First available position
```

### createDefaultGridConfig

Creates default grid configuration.

```tsx
function createDefaultGridConfig(): GridConfig
```

#### Example

```tsx
import { createDefaultGridConfig } from 'widget-package';

const defaultConfig = createDefaultGridConfig();
// Result:
// {
//   cols: 12,
//   rowHeight: 60,
//   width: 1200,
//   margin: [16, 16],
//   containerPadding: [16, 16],
//   isDraggable: true,
//   isResizable: true,
//   isDroppable: true
// }

// Use with Layout component
<Layout
  type="grid"
  {...defaultConfig}
  onLayoutChange={handleLayoutChange}
>
  {widgets}
</Layout>
```

## Serialization

### WidgetRegistry

Manages widget components for serialization and deserialization.

```tsx
class WidgetRegistry {
  register(id: string, component: React.ComponentType<any>): void
  get(id: string): React.ComponentType<any> | undefined
  has(id: string): boolean
  unregister(id: string): void
  clear(): void
  getAll(): Map<string, React.ComponentType<any>>
}
```

#### Example

```tsx
import { WidgetRegistry } from 'widget-package';

// Create registry
const registry = new WidgetRegistry();

// Register components
registry.register('counter', CounterWidget);
registry.register('chart', ChartWidget);
registry.register('todo', TodoWidget);

// Check if component exists
if (registry.has('counter')) {
  const CounterComponent = registry.get('counter');
}

// Get all registered components
const allComponents = registry.getAll();
```

### serializeWidget

Serializes a widget for storage or transmission.

```tsx
function serializeWidget(widget: Widget): SerializedWidget
```

#### Example

```tsx
import { serializeWidget } from 'widget-package';

const widget = createWidget('counter', 'Counter', CounterWidget, {
  props: { initialValue: 10 }
});

const serialized = serializeWidget(widget);
// Result:
// {
//   id: 'counter',
//   name: 'Counter',
//   description: 'Counter widget',
//   category: 'General',
//   tags: ['counter'],
//   props: { initialValue: 10 },
//   layout: { w: 4, h: 3, x: 0, y: 0, minW: 2, minH: 2 },
//   componentId: 'counter'  // Component reference
// }

// Store in localStorage
localStorage.setItem('widget', JSON.stringify(serialized));
```

### deserializeWidget

Deserializes a widget and restores the component.

```tsx
function deserializeWidget(
  serialized: SerializedWidget,
  registry: WidgetRegistry
): Widget | null
```

#### Example

```tsx
import { deserializeWidget, WidgetRegistry } from 'widget-package';

// Setup registry
const registry = new WidgetRegistry();
registry.register('counter', CounterWidget);

// Deserialize from storage
const serialized = JSON.parse(localStorage.getItem('widget'));
const widget = deserializeWidget(serialized, registry);

if (widget) {
  // Widget successfully restored with component
  console.log('Restored widget:', widget.name);
}
```

### Complete Serialization Example

```tsx
function useWidgetPersistence() {
  const { widgets, addWidget } = useWidgets();
  const registry = new WidgetRegistry();

  // Register all components
  useEffect(() => {
    registry.register('counter', CounterWidget);
    registry.register('chart', ChartWidget);
    registry.register('todo', TodoWidget);
  }, []);

  const saveWidgets = () => {
    const serialized = widgets.map(serializeWidget);
    localStorage.setItem('dashboard-widgets', JSON.stringify(serialized));
  };

  const loadWidgets = () => {
    const saved = localStorage.getItem('dashboard-widgets');
    if (saved) {
      const serialized = JSON.parse(saved);
      const restored = serialized
        .map(s => deserializeWidget(s, registry))
        .filter(Boolean);
      
      restored.forEach(addWidget);
    }
  };

  return { saveWidgets, loadWidgets };
}
```

## Validation

### validateWidget

Validates widget configuration.

```tsx
function validateWidget(widget: Widget): ValidationResult
```

#### Example

```tsx
import { validateWidget } from 'widget-package';

const widget = createWidget('test', '', TestComponent); // Invalid: empty name

const result = validateWidget(widget);
// Result:
// {
//   isValid: false,
//   errors: [
//     'Widget name cannot be empty',
//     'Widget component is required'
//   ]
// }

if (!result.isValid) {
  console.error('Widget validation failed:', result.errors);
}
```

### Custom Validation

```tsx
function validateWidgetWithCustomRules(widget: Widget): ValidationResult {
  const baseResult = validateWidget(widget);
  const customErrors = [];

  // Custom validation rules
  if (widget.layout && widget.layout.w > 12) {
    customErrors.push('Widget width cannot exceed 12 columns');
  }

  if (widget.tags && widget.tags.length > 5) {
    customErrors.push('Widget cannot have more than 5 tags');
  }

  if (widget.name.length > 50) {
    customErrors.push('Widget name cannot exceed 50 characters');
  }

  return {
    isValid: baseResult.isValid && customErrors.length === 0,
    errors: [...baseResult.errors, ...customErrors]
  };
}
```

## Helper Functions

### generateWidgetInstanceId

Generates unique IDs for widget instances.

```tsx
function generateWidgetInstanceId(baseId: string): string
```

#### Example

```tsx
import { generateWidgetInstanceId } from 'widget-package';

const baseId = 'counter';
const instanceId1 = generateWidgetInstanceId(baseId);
const instanceId2 = generateWidgetInstanceId(baseId);

console.log(instanceId1); // 'counter-1640995200000-abc123def'
console.log(instanceId2); // 'counter-1640995200001-xyz789ghi'

// Use when dropping widgets
const handleDrop = (widget: Widget) => {
  const instance = {
    ...widget,
    id: generateWidgetInstanceId(widget.id)
  };
  addWidgetToLayout(instance);
};
```

### filterWidgets

Filters widgets based on criteria.

```tsx
function filterWidgets(
  widgets: Widget[],
  criteria: FilterCriteria
): Widget[]
```

#### Example

```tsx
import { filterWidgets } from 'widget-package';

const widgets = [/* widget array */];

// Filter by category
const chartWidgets = filterWidgets(widgets, {
  category: 'Charts'
});

// Filter by tags
const interactiveWidgets = filterWidgets(widgets, {
  tags: ['interactive']
});

// Complex filter
const filteredWidgets = filterWidgets(widgets, {
  category: 'Analytics',
  tags: ['chart', 'data'],
  search: 'line'
});
```

### groupWidgetsByCategory

Groups widgets by category.

```tsx
function groupWidgetsByCategory(widgets: Widget[]): Record<string, Widget[]>
```

#### Example

```tsx
import { groupWidgetsByCategory } from 'widget-package';

const widgets = [/* widget array */];
const grouped = groupWidgetsByCategory(widgets);

// Result:
// {
//   'Charts': [chartWidget1, chartWidget2],
//   'Interactive': [counterWidget, formWidget],
//   'Analytics': [summaryWidget]
// }

// Render grouped widgets
Object.entries(grouped).map(([category, widgets]) => (
  <div key={category}>
    <h3>{category}</h3>
    {widgets.map(widget => (
      <WidgetCard key={widget.id} widget={widget} />
    ))}
  </div>
));
```

## Examples

### Complete Widget Factory

```tsx
import {
  createWidget,
  createCategory,
  validateWidget,
  WidgetRegistry
} from 'widget-package';

class WidgetFactory {
  private registry = new WidgetRegistry();

  constructor() {
    // Register all available components
    this.registry.register('counter', CounterWidget);
    this.registry.register('chart', ChartWidget);
    this.registry.register('todo', TodoWidget);
  }

  createWidget(config: WidgetConfig): Widget | null {
    const { componentId, ...options } = config;
    
    // Get component from registry
    const component = this.registry.get(componentId);
    if (!component) {
      console.error(`Component '${componentId}' not found in registry`);
      return null;
    }

    // Create widget
    const widget = createWidget(
      config.id,
      config.name,
      component,
      options
    );

    // Validate
    const validation = validateWidget(widget);
    if (!validation.isValid) {
      console.error('Widget validation failed:', validation.errors);
      return null;
    }

    return widget;
  }

  createCategory(id: string, name: string, widgetConfigs: WidgetConfig[]) {
    const widgets = widgetConfigs
      .map(config => this.createWidget(config))
      .filter(Boolean) as Widget[];

    return createCategory(id, name, widgets);
  }

  getAvailableComponents() {
    return Array.from(this.registry.getAll().keys());
  }
}

// Usage
const factory = new WidgetFactory();

const widget = factory.createWidget({
  id: 'my-counter',
  name: 'My Counter',
  componentId: 'counter',
  category: 'Interactive',
  props: { initialValue: 5 }
});

const category = factory.createCategory('interactive', 'Interactive Widgets', [
  { id: 'counter1', name: 'Counter 1', componentId: 'counter' },
  { id: 'counter2', name: 'Counter 2', componentId: 'counter' }
]);
```

### Layout Manager

```tsx
import {
  findOptimalPosition,
  widgetToGridLayout,
  createDefaultGridConfig
} from 'widget-package';

class LayoutManager {
  private config = createDefaultGridConfig();
  private layouts: Layout[] = [];

  addWidget(widget: Widget): Layout {
    // Find optimal position
    const position = findOptimalPosition(this.layouts, widget, this.config.cols);
    
    // Create layout with position
    const layout = {
      ...widgetToGridLayout(widget),
      x: position.x,
      y: position.y
    };

    this.layouts.push(layout);
    return layout;
  }

  removeWidget(widgetId: string) {
    this.layouts = this.layouts.filter(layout => layout.i !== widgetId);
  }

  updateLayout(layouts: Layout[]) {
    this.layouts = layouts;
  }

  getCompactLayout(): Layout[] {
    // Implement layout compaction logic
    return this.layouts.sort((a, b) => {
      if (a.y !== b.y) return a.y - b.y;
      return a.x - b.x;
    });
  }

  exportLayout() {
    return {
      config: this.config,
      layouts: this.layouts
    };
  }

  importLayout(data: { config: any; layouts: Layout[] }) {
    this.config = { ...this.config, ...data.config };
    this.layouts = data.layouts;
  }
}
```

---

This utilities documentation provides comprehensive examples and patterns for using all utility functions in the Widget Package. For more information, see the [API Reference](API.md) and [Component Documentation](COMPONENTS.md).
