# 📚 Widget Package Examples

This directory contains comprehensive examples showing different ways to use the Widget Package in real-world scenarios.

## 🗂️ Examples Overview

### 1. **Basic Integration** (`basic/`)
- Simple widget catalog setup
- Basic widget creation
- Minimal configuration

### 2. **Dashboard Application** (`dashboard/`)
- Complete dashboard with drag & drop
- Multiple widget types
- Layout persistence
- Real-time data updates

### 3. **Admin Panel** (`admin-panel/`)
- Complex widget system
- User permissions
- Widget marketplace
- Custom themes

### 4. **Analytics Dashboard** (`analytics/`)
- Chart widgets
- Data visualization
- Performance monitoring
- Export functionality

### 5. **CMS Integration** (`cms/`)
- Content management widgets
- Dynamic widget loading
- Widget configuration UI
- Multi-tenant support

## 🚀 Running Examples

Each example is a complete React application that you can run independently:

```bash
# Navigate to an example directory
cd examples/basic

# Install dependencies
npm install

# Start the development server
npm start
```

## 📋 Example Structure

Each example follows this structure:

```
example-name/
├── src/
│   ├── components/          # React components
│   ├── widgets/            # Custom widgets
│   ├── utils/              # Utility functions
│   ├── styles/             # CSS/styling
│   ├── App.tsx             # Main application
│   └── index.tsx           # Entry point
├── public/                 # Static assets
├── package.json            # Dependencies
└── README.md              # Example-specific documentation
```

## 🎯 Learning Path

We recommend exploring the examples in this order:

1. **Basic Integration** - Learn the fundamentals
2. **Dashboard Application** - Understand layout systems
3. **Analytics Dashboard** - Work with data visualization
4. **Admin Panel** - Explore advanced features
5. **CMS Integration** - See enterprise patterns

## 🔧 Customization

Each example includes:

- **Detailed comments** explaining key concepts
- **Configuration options** you can modify
- **Extension points** for adding features
- **Best practices** demonstrated in code

## 📖 Documentation

- Each example has its own README with specific instructions
- Code is heavily commented for learning purposes
- Common patterns are explained with inline documentation
- Performance considerations are highlighted

## 🤝 Contributing

Found an issue or want to add an example?

1. Fork the repository
2. Create a new example directory
3. Follow the existing structure
4. Add comprehensive documentation
5. Submit a pull request

## 💡 Tips

- Start with the basic example to understand core concepts
- Copy and modify examples for your own projects
- Check the main documentation for API reference
- Use the testing utilities provided in each example

---

Happy coding! 🎉
