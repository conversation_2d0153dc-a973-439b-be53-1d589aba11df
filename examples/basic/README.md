# 🚀 Basic Widget Package Example

This example demonstrates the simplest way to get started with the Widget Package. It shows how to create a basic widget catalog with a few custom widgets.

## 📋 What You'll Learn

- How to set up the WidgetsProvider
- Creating your first custom widget
- Using the WidgetsCatalog component
- Basic widget configuration

## 🏗️ Project Structure

```
basic/
├── src/
│   ├── widgets/
│   │   ├── WelcomeWidget.tsx    # Simple welcome widget
│   │   ├── InfoWidget.tsx       # Information display widget
│   │   └── index.ts             # Widget exports
│   ├── App.tsx                  # Main application
│   └── index.tsx                # Entry point
├── package.json
└── README.md
```

## 🚀 Getting Started

1. **Install dependencies:**
   ```bash
   npm install
   ```

2. **Start the development server:**
   ```bash
   npm start
   ```

3. **Open your browser:**
   Navigate to `http://localhost:3000`

## 🧩 Widgets Included

### WelcomeWidget
A simple welcome message widget that demonstrates:
- Basic widget structure
- Props handling
- Simple styling

### InfoWidget
An information display widget that shows:
- Dynamic content
- Icon integration
- Responsive design

## 🔧 Key Concepts Demonstrated

### 1. Widget Creation
```tsx
// Basic widget structure
export const WelcomeWidget: React.FC<WelcomeWidgetProps> = ({ 
  title = "Welcome",
  message = "Hello, World!" 
}) => {
  return (
    <div className="welcome-widget">
      <h2>{title}</h2>
      <p>{message}</p>
    </div>
  );
};
```

### 2. Widget Registration
```tsx
// Using the createWidget utility
export const welcomeWidget = createWidget(
  'welcome-widget',
  'Welcome Widget',
  WelcomeWidget,
  {
    description: 'A simple welcome message widget',
    category: 'Basic',
    tags: ['welcome', 'greeting'],
    props: {
      title: 'Welcome to Widget Package!',
      message: 'This is your first widget.'
    }
  }
);
```

### 3. Provider Setup
```tsx
// App.tsx
function App() {
  return (
    <WidgetsProvider initialWidgets={widgets}>
      <div className="app">
        <h1>Basic Widget Example</h1>
        <WidgetsCatalog 
          showSearch={true}
          showCategories={true}
        />
      </div>
    </WidgetsProvider>
  );
}
```

## 🎨 Customization

### Adding Your Own Widget

1. **Create a new widget component:**
   ```tsx
   // src/widgets/MyWidget.tsx
   import React from 'react';

   interface MyWidgetProps {
     title?: string;
   }

   export const MyWidget: React.FC<MyWidgetProps> = ({ title = "My Widget" }) => {
     return (
       <div style={{ padding: '16px', textAlign: 'center' }}>
         <h3>{title}</h3>
         <p>This is my custom widget!</p>
       </div>
     );
   };
   ```

2. **Register the widget:**
   ```tsx
   // src/widgets/index.ts
   import { createWidget } from 'widget-package';
   import { MyWidget } from './MyWidget';

   export const myWidget = createWidget(
     'my-widget',
     'My Widget',
     MyWidget,
     {
       description: 'My custom widget',
       category: 'Custom',
       props: { title: 'Hello from my widget!' }
     }
   );
   ```

3. **Add to the widgets array:**
   ```tsx
   // src/App.tsx
   import { myWidget } from './widgets';

   const widgets = [welcomeWidget, infoWidget, myWidget];
   ```

### Styling

This example uses simple CSS for styling. You can:

- Add CSS modules
- Use styled-components
- Integrate with your design system
- Add CSS-in-JS solutions

### Props Configuration

Experiment with different widget props:

```tsx
const customWelcomeWidget = createWidget(
  'custom-welcome',
  'Custom Welcome',
  WelcomeWidget,
  {
    props: {
      title: 'Custom Title',
      message: 'Your custom message here',
      backgroundColor: '#f0f8ff',
      textColor: '#333'
    }
  }
);
```

## 📚 Next Steps

After completing this example, try:

1. **Dashboard Example** - Learn about layouts and drag & drop
2. **Analytics Example** - Work with data visualization
3. **Admin Panel Example** - Explore advanced features

## 🐛 Troubleshooting

### Common Issues

1. **Widgets not showing:**
   - Check that widgets are properly imported
   - Verify the WidgetsProvider is wrapping your components

2. **Styling issues:**
   - Ensure CSS is imported
   - Check for conflicting styles

3. **TypeScript errors:**
   - Verify prop interfaces match widget expectations
   - Check that all required props are provided

### Getting Help

- Check the main documentation
- Look at other examples
- Open an issue on GitHub

---

This basic example provides a solid foundation for understanding the Widget Package. Experiment with the code and try creating your own widgets!
