import { useState, useMemo, useEffect } from "react";
import { WidgetsProvider, WidgetsCatalog, Layout } from "./lib";
import type { Widget, LayoutType } from "./lib";
import type { Layout as RGLLayout } from "react-grid-layout";
import { Model, TabNode, Actions, DockLocation } from "flexlayout-react";
import { CounterWidget } from "./example-widgets/CounterWidget";
import { GreetingWidget } from "./example-widgets/GreetingWidget";
import { TodoWidget } from "./example-widgets/TodoWidget";
import "./App.css";
import "react-grid-layout/css/styles.css";
import "react-resizable/css/styles.css";
import "flexlayout-react/style/light.css";
import { WidgetErrorBoundary } from './lib/WidgetErrorBoundary';

// Create a widget registry to map component IDs to actual components
// This is necessary because React components cannot be serialized through JSON.stringify()
// during drag and drop operations, so we need to restore them from a registry
const widgetRegistry = {
  counter: CounterWidget,
  greeting: GreetingWidget,
  todo: TodoWidget,
};

// Helper function to get component from registry
const getWidgetComponent = (widgetId: string) => {
  return widgetRegistry[widgetId as keyof typeof widgetRegistry];
};

// Define example widgets
const exampleWidgets: Widget[] = [
  {
    id: "counter",
    name: "Counter Widget",
    description: "A simple counter that can increment and decrement values",
    component: CounterWidget,
    category: "Interactive",
    tags: ["counter", "interactive", "demo"],
    props: { initialValue: 0, step: 1 },
    layout: { w: 3, h: 4, x: 0, y: 0 },
  },
  {
    id: "greeting",
    name: "Greeting Widget",
    description: "A personalized greeting widget with name input",
    component: GreetingWidget,
    category: "Interactive",
    tags: ["greeting", "input", "demo"],
    props: { defaultName: "Developer" },
    layout: { w: 4, h: 3, x: 3, y: 0 },
  },
  {
    id: "todo",
    name: "Todo List Widget",
    description: "A simple todo list to manage tasks",
    component: TodoWidget,
    category: "Productivity",
    tags: ["todo", "list", "productivity", "tasks"],
    layout: { w: 5, h: 6, x: 7, y: 0 },
  },
];

function App() {
  const [selectedWidget, setSelectedWidget] = useState<Widget | null>(null);
  const [layoutType, setLayoutType] = useState<LayoutType>("grid");
  // Pre-populate with example widgets to help developers understand the layout
  const [droppedWidgets, setDroppedWidgets] = useState<Widget[]>([]);
  const [currentLayout, setCurrentLayout] = useState<RGLLayout[]>([
    { i: "demo-counter-1", x: 0, y: 0, w: 4, h: 3 },
    { i: "demo-greeting-1", x: 4, y: 0, w: 4, h: 3 },
  ]);
  const [flexModel, setFlexModel] = useState<Model | null>(null);

  // Create FlexLayout model
  const initialFlexModel = useMemo(() => {
    const json = {
      global: {
        tabEnableClose: true,
        tabEnableRename: false,
        tabSetEnableTabStrip: true,
        tabSetEnableDrop: true,
        tabSetEnableDrag: true,
        tabSetEnableMaximize: true,
      },
      borders: [],
      layout: {
        type: "row",
        weight: 100,
        children: [
          {
            type: "tabset",
            weight: 100,
            children: [
              {
                type: "tab",
                name: "Welcome",
                component: "welcome",
                id: "welcome-tab",
              },
            ],
          },
        ],
      },
    };
    console.log("Creating FlexLayout model with JSON:", json);
    return Model.fromJson(json);
  }, []);

  // Initialize flex model on mount
  useEffect(() => {
    if (!flexModel) {
      console.log("Initializing FlexLayout model:", initialFlexModel);
      setFlexModel(initialFlexModel);
    }
  }, [initialFlexModel, flexModel]);

  // FlexLayout factory function
  const flexFactory = (node: TabNode) => {
    const component = node.getComponent();
    const config = node.getConfig() || {};
    const widgetId = config.widgetId;

    console.log("FlexLayout factory called:", { component, widgetId, config });

    // Handle welcome tab
    if (component === "welcome") {
      return (
        <div className="flexlayout-welcome">
          <div className="welcome-header">
            <div className="welcome-icon">📑</div>
            <h3 className="welcome-title">Welcome to FlexLayout!</h3>
            <p className="welcome-subtitle">
              Create powerful tabbed interfaces with drag & drop
            </p>
          </div>

          <div className="welcome-content">
            <div className="welcome-section">
              <div className="section-icon">🎯</div>
              <h4 className="section-title">Getting Started</h4>
              <p className="section-description">
                Drag widgets from the catalog to add them as interactive tabs in
                your layout
              </p>
            </div>

            <div className="welcome-features">
              <h4 className="features-title">What you can do:</h4>
              <div className="features-grid">
                <div className="feature-card">
                  <span className="feature-icon">🔄</span>
                  <div className="feature-content">
                    <strong>Reorder Tabs</strong>
                    <p>Drag tabs to change their order</p>
                  </div>
                </div>
                <div className="feature-card">
                  <span className="feature-icon">📐</span>
                  <div className="feature-content">
                    <strong>Create Tabsets</strong>
                    <p>Drag tabs to create new sections</p>
                  </div>
                </div>
                <div className="feature-card">
                  <span className="feature-icon">🔍</span>
                  <div className="feature-content">
                    <strong>Maximize View</strong>
                    <p>Use maximize button for full screen</p>
                  </div>
                </div>
                <div className="feature-card">
                  <span className="feature-icon">❌</span>
                  <div className="feature-content">
                    <strong>Close Tabs</strong>
                    <p>Click X button to remove tabs</p>
                  </div>
                </div>
              </div>
            </div>

            <div className="welcome-cta">
              <div className="cta-icon">✨</div>
              <p className="cta-text">
                Start by dragging a widget from the catalog!
              </p>
            </div>
          </div>
        </div>
      );
    }

    // Handle widget tabs
    if (component === "widget" && widgetId) {
      const WidgetComponent = getWidgetComponent(widgetId);
      if (WidgetComponent) {
        // Find the widget by original ID (before timestamp)
        const widget = exampleWidgets.find((w) => w.id === widgetId);
        console.log("Found widget:", widget);
        return (
          <div style={{ padding: "10px", height: "100%" }}>
            <WidgetComponent {...(widget?.props || {})} />
          </div>
        );
      }
    }

    return (
      <div style={{ padding: "20px", textAlign: "center", color: "#666" }}>
        <div>Component: {component || "undefined"}</div>
        <div>Widget ID: {widgetId || "undefined"}</div>
        <div>Config: {JSON.stringify(config)}</div>
        <div>Unknown component type</div>
      </div>
    );
  };

  const handleWidgetSelect = (widget: Widget) => {
    setSelectedWidget(widget);
  };

  const handleDrop = (layout: RGLLayout[], item: RGLLayout, e: Event) => {
    console.log("Drop event:", { layout, item, e });

    // Get the drag data from the event
    const dragEvent = e as DragEvent;
    const dragData = dragEvent.dataTransfer?.getData("application/json");

    if (dragData) {
      try {
        const data = JSON.parse(dragData);
        if (data.widget) {
          // Restore the component from the registry using the widget ID
          const component = getWidgetComponent(data.widget.id);

          if (component !== undefined) {
            const newWidget = {
              ...data.widget,
              component, // Restore the actual component
              id: `${data.widget.id}-${Date.now()}`, // Make unique ID for dropped instance
              layout: {
                ...data.widget.layout,
                x: item.x,
                y: item.y,
                w: Math.max(item.w, 4), // Minimum width of 4 grid units
                h: Math.max(item.h, 3), // Minimum height of 3 grid units
              },
            };

            if (layoutType === "grid") {
              // For grid layout, add to dropped widgets
              setDroppedWidgets((prev) => [...prev, newWidget]);
            } else if (layoutType === "flex" && flexModel) {
              // For flex layout, add tab to FlexLayout model
              try {
                // Find the first tabset in the model
                const firstTabset = flexModel.getFirstTabSet();
                const tabsetId = firstTabset ? firstTabset.getId() : "1";

                console.log("Adding tab to FlexLayout:", {
                  tabsetId,
                  widgetName: data.widget.name,
                  widgetId: data.widget.id,
                  currentModel: flexModel,
                });

                const action = Actions.addNode(
                  {
                    type: "tab",
                    name: data.widget.name,
                    component: "widget",
                    config: { widgetId: data.widget.id },
                  },
                  tabsetId,
                  DockLocation.CENTER,
                  -1
                );

                console.log("FlexLayout action:", action);
                const newModel = flexModel.doAction(action);
                console.log("New FlexLayout model:", newModel);

                if (newModel && typeof newModel.getRoot === "function") {
                  setFlexModel(newModel);
                } else {
                  console.error(
                    "Invalid model returned from doAction:",
                    newModel
                  );
                }
              } catch (error) {
                console.error("Error adding tab to FlexLayout:", error);
              }
              // Don't add to droppedWidgets for flex layout as it's handled by FlexLayout
            }
          } else {
            console.error(
              "Widget component not found in registry:",
              data.widget.id
            );
          }
        }
      } catch (error) {
        console.error("Error parsing drag data:", error);
      }
    }
  };

  const handleLayoutChange = (layout: RGLLayout[]) => {
    setCurrentLayout(layout);

    // Update the widget layout properties to match the new positions
    setDroppedWidgets(prevWidgets =>
      prevWidgets.map(widget => {
        const layoutItem = layout.find(item => item.i === widget.id);
        if (layoutItem) {
          return {
            ...widget,
            layout: {
              x: layoutItem.x,
              y: layoutItem.y,
              w: layoutItem.w,
              h: layoutItem.h
            }
          };
        }
        return widget;
      })
    );
  };

  const handleDrag = (widget: Widget) => {
    console.log("Dragging widget:", widget.name);
  };

  const handleClearLayout = () => {
    setDroppedWidgets([]);
    setCurrentLayout([]);
    if (layoutType === "flex" && flexModel) {
      // Reset FlexLayout to initial state
      setFlexModel(initialFlexModel);
    }
  };

  const handleAddDemoWidgets = () => {
    // Add demo widgets for testing
    const demoWidgets = [
      {
        ...exampleWidgets[0],
        id: `demo-counter-${Date.now()}`,
        component: getWidgetComponent("counter"),
        layout: { x: 0, y: 0, w: 4, h: 3 },
      },
      {
        ...exampleWidgets[1],
        id: `demo-greeting-${Date.now()}`,
        component: getWidgetComponent("greeting"),
        layout: { x: 4, y: 0, w: 4, h: 3 },
      },
      {
        ...exampleWidgets[2],
        id: `demo-todo-${Date.now()}`,
        component: getWidgetComponent("todo"),
        layout: { x: 8, y: 0, w: 4, h: 4 },
      },
    ];
    setDroppedWidgets(demoWidgets);
    setCurrentLayout([
      { i: demoWidgets[0].id, x: 0, y: 0, w: 4, h: 3 },
      { i: demoWidgets[1].id, x: 4, y: 0, w: 4, h: 3 },
      { i: demoWidgets[2].id, x: 8, y: 0, w: 4, h: 4 },
    ]);
  };

  const renderDroppedWidgets = () => {
    return droppedWidgets.map((widget) => {
      const WidgetComponent = widget.component;

      // Find the current layout position for this widget
      const currentLayoutItem = currentLayout.find(item => item.i === widget.id);
      const gridData = currentLayoutItem || widget.layout;

      // Safety check to ensure component exists
      if (!WidgetComponent) {
        console.error("Widget component is undefined for widget:", widget.id);
        return (
          <div
            key={widget.id}
            data-grid={gridData}
            className="grid-widget-container"
          >
            <div className="grid-widget-content">
              <div
                className="widget-header"
                style={{
                  background:
                    "linear-gradient(135deg, #e74c3c 0%, #c0392b 100%)",
                }}
              >
                <div className="widget-title">
                  <span className="widget-icon">⚠️</span>
                  <span className="widget-name">{widget.name}</span>
                </div>
                <div className="widget-actions">
                  <button
                    className="widget-action-btn"
                    onClick={() => {
                      setDroppedWidgets((prev) =>
                        prev.filter((w) => w.id !== widget.id)
                      );
                      setCurrentLayout((prev) =>
                        prev.filter((l) => l.i !== widget.id)
                      );
                    }}
                    title="Remove widget"
                    aria-label="Remove widget"
                  >
                    ✕
                  </button>
                </div>
              </div>
              <div className="widget-body">
                <div
                  style={{
                    display: "flex",
                    flexDirection: "column",
                    alignItems: "center",
                    justifyContent: "center",
                    height: "100%",
                    color: "#e74c3c",
                    textAlign: "center",
                  }}
                >
                  <div style={{ fontSize: "32px", marginBottom: "12px" }}>
                    🚫
                  </div>
                  <div style={{ fontWeight: "bold", marginBottom: "8px" }}>
                    Component Error
                  </div>
                  <div style={{ fontSize: "14px", color: "#6c757d" }}>
                    Widget component not found
                  </div>
                </div>
              </div>
            </div>
          </div>
        );
      }

      return (
        <div
          key={widget.id}
          data-grid={gridData}
          className="grid-widget-container"
        >
          <div className="grid-widget-content">
            <div className="widget-header">
              <div className="widget-title drag-handle">
                <span className="widget-icon">🧩</span>
                <span className="widget-name">{widget.name}</span>
                <span className="drag-indicator" title="Drag to move">⋮⋮</span>
              </div>
              <div className="widget-actions">
                <button
                  className="widget-action-btn"
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    setDroppedWidgets((prev) =>
                      prev.filter((w) => w.id !== widget.id)
                    );
                    setCurrentLayout((prev) =>
                      prev.filter((l) => l.i !== widget.id)
                    );
                  }}
                  onMouseDown={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                  }}
                  title="Remove widget"
                  aria-label="Remove widget"
                  style={{
                    pointerEvents: 'auto',
                    zIndex: 1000,
                    position: 'relative'
                  }}
                >
                  ✕
                </button>
              </div>
            </div>
            <div
              className="widget-body"
              onMouseDown={(e) => {
                // Only prevent dragging if clicking on interactive elements
                const target = e.target as HTMLElement;
                if (target.tagName === 'BUTTON' || target.tagName === 'INPUT' || target.tagName === 'SELECT' || target.tagName === 'TEXTAREA') {
                  e.stopPropagation();
                }
              }}
            >
              <WidgetErrorBoundary widgetName={widget.name}>
                <WidgetComponent key={`widget-${widget.id}`} {...(widget.props || {})} />
              </WidgetErrorBoundary>
            </div>

          </div>
        </div>
      );
    });
  };

  const renderSelectedWidget = () => {
    if (!selectedWidget) return null;

    const WidgetComponent = selectedWidget.component;
    return (
      <div
        style={{
          marginTop: "20px",
          padding: "20px",
          border: "1px solid #ddd",
          borderRadius: "8px",
        }}
      >
        <h3>Selected Widget: {selectedWidget.name}</h3>
        <WidgetComponent {...(selectedWidget.props || {})} />
      </div>
    );
  };

  return (
    <WidgetsProvider initialWidgets={exampleWidgets}>
      <div
        style={{
          minHeight: "100vh",
          width: "100%",
        }}
      >
        <header
          style={{
            textAlign: "center",
            marginBottom: "40px",
            padding: "40px 20px",
            backgroundColor: "#fff",
            borderRadius: "16px",
            boxShadow: "0 4px 16px rgba(0,0,0,0.1)",
            background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
            color: "white",
          }}
        >
          <h1
            style={{
              margin: "0 0 16px 0",
              fontSize: "48px",
              fontWeight: "700",
              textShadow: "0 2px 4px rgba(0,0,0,0.3)",
            }}
          >
            🚀 Tech4Fab Widget Package
          </h1>
          <p
            style={{
              fontSize: "20px",
              opacity: 0.9,
              lineHeight: "1.5",
              maxWidth: "600px",
              margin: "0 auto",
            }}
          >
            Interactive demonstration of WidgetsProvider, WidgetsCatalog, and
            dual Layout components with drag & drop functionality
          </p>
        </header>

        <div
          style={{
            marginBottom: "32px",
            backgroundColor: "#fff",
            padding: "24px",
            borderRadius: "16px",
            boxShadow: "0 2px 12px rgba(0,0,0,0.1)",
            border: "1px solid #e9ecef",
          }}
        >
          <div className="layout-controls-container">
            <div className="layout-controls-header">
              <div className="layout-controls-title-section">
                <div className="layout-controls-icon">⚙️</div>
                <div>
                  <h2 className="layout-controls-title">Layout Controls</h2>
                  <p className="layout-controls-subtitle">
                    Configure your widget layout experience
                  </p>
                </div>
              </div>
              <div className="layout-status-badge">
                <span className="status-indicator"></span>
                <span className="status-text">
                  {droppedWidgets.length} widgets active
                </span>
              </div>
            </div>

            <div className="layout-controls-content">
              <div className="layout-controls-section">
                <div className="control-group">
                  <label className="control-label">
                    <span className="label-icon">🎯</span>
                    <span className="label-text">Layout Engine</span>
                  </label>
                  <div className="select-wrapper">
                    <select
                      value={layoutType}
                      onChange={(e) =>
                        setLayoutType(e.target.value as LayoutType)
                      }
                      className="layout-type-select"
                      aria-label="Select layout type"
                      title="Select layout type"
                      style={{ color: "#2c3e50" }}
                    >
                      <option style={{ color: "#2c3e50" }} value="grid">
                        🔲 Grid Layout (react-grid-layout)
                      </option>
                      <option style={{ color: "#2c3e50" }} value="flex">
                        📑 Flex Layout (flexlayout-react)
                      </option>
                    </select>
                    <div className="select-arrow">
                      <svg width="12" height="8" viewBox="0 0 12 8" fill="none">
                        <path
                          d="M1 1L6 6L11 1"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        />
                      </svg>
                    </div>
                  </div>
                </div>
              </div>

              <div className="layout-controls-actions">
                <button
                  onClick={handleClearLayout}
                  className="control-button control-button--danger"
                  aria-label="Clear all widgets from layout"
                  title="Remove all widgets from the layout"
                  disabled={droppedWidgets.length === 0}
                >
                  <span className="button-icon">🗑️</span>
                  <span className="button-text">Clear Layout</span>
                  <span className="button-badge">{droppedWidgets.length}</span>
                </button>
                <button
                  onClick={handleAddDemoWidgets}
                  className="control-button control-button--success"
                  aria-label="Add demo widgets to layout"
                  title="Add sample widgets for testing"
                >
                  <span className="button-icon">✨</span>
                  <span className="button-text">Add Demo Widgets</span>
                  <span className="button-badge">3</span>
                </button>
              </div>
            </div>
          </div>
        </div>

        <div style={{ display: "flex", gap: "24px", minHeight: "1000px" }}>
          {/* Widget Catalog */}
          <div
            style={{
              width: "350px",
              flexShrink: 0,
              backgroundColor: "#fff",
              border: "1px solid #e0e0e0",
              borderRadius: "12px",
              padding: "20px",
              boxShadow: "0 2px 8px rgba(0,0,0,0.1)",
              height: "fit-content",
            }}
          >
            <div style={{ marginBottom: "16px" }}>
              <h3
                style={{
                  margin: "0 0 8px 0",
                  fontSize: "20px",
                  fontWeight: "600",
                  color: "#2c3e50",
                }}
              >
                🧩 Widget Catalog
              </h3>
              <p
                style={{
                  fontSize: "14px",
                  color: "#7f8c8d",
                  margin: 0,
                  lineHeight: "1.4",
                }}
              >
                Drag widgets to the layout area to add them →
              </p>
            </div>
            <WidgetsCatalog
              widgets={exampleWidgets}
              onWidgetSelect={handleWidgetSelect}
              onDrag={handleDrag}
              enableDrag={true}
              showSearch={true}
              showCategories={true}
            />
          </div>

          {/* Layout Area */}
          <div
            style={{
              flex: 1,
              border: "2px dashed #3498db",
              borderRadius: "16px",
              minHeight: "900px",
              display: "flex",
              flexDirection: "column",
              backgroundColor: "#fff",
              boxShadow: "0 4px 16px rgba(0,0,0,0.1)",
              overflow: "hidden",
            }}
          >
            <div
              style={{
                padding: "20px",
                borderBottom: "1px solid #ecf0f1",
                backgroundColor:
                  "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
                background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
                color: "white",
              }}
            >
              <h3
                style={{
                  margin: "0 0 8px 0",
                  fontSize: "24px",
                  fontWeight: "600",
                }}
              >
                {`🎯 Layout Area (${layoutType})`}
              </h3>
              <p
                style={{
                  margin: 0,
                  fontSize: "16px",
                  opacity: 0.9,
                  lineHeight: "1.4",
                }}
              >
                {`Drop widgets here to see them in the ${layoutType} layout • ${droppedWidgets.length} widgets active`}
              </p>
            </div>
            <div
              style={{
                flex: 1,
                display: "flex",
                flexDirection: "column",
                minHeight: "800px",
                padding: "20px",
                backgroundColor: "#fafbfc",
                overflow: "hidden",
              }}
            >
              <div className="layout-container">
                <Layout
                  type={layoutType}
                  onDrop={handleDrop}
                  onLayoutChange={handleLayoutChange}
                  layout={currentLayout}
                  cols={12}
                  rowHeight={50} // Even larger row height for better visibility
                  width={1400} // Increased width for more space
                  margin={[16, 16]} // Larger margins
                  containerPadding={[24, 24]} // More padding
                  isDraggable={true}
                  isResizable={true}
                  isDroppable={true}
                  flexModel={flexModel}
                  flexFactory={flexFactory}
                  onFlexModelChange={setFlexModel}
                  flexDirection="row"
                  flexWrap="wrap"
                  gap="20px"
                >
                  {layoutType === "grid" ? (
                    droppedWidgets.length === 0 ? (
                      <div style={{
                        display: 'flex',
                        flexDirection: 'column',
                        alignItems: 'center',
                        justifyContent: 'center',
                        height: '100%',
                        color: '#888',
                        fontSize: 20,
                        opacity: 0.7
                      }}>
                        <div style={{ fontSize: 48, marginBottom: 12 }}>🧩</div>
                        <div>Drop widgets here to get started!</div>
                      </div>
                    ) : renderDroppedWidgets()
                  ) : null}
                </Layout>
              </div>
            </div>
          </div>
        </div>

        {renderSelectedWidget()}
        <footer
          style={{
            marginTop: "48px",
            padding: "32px",
            backgroundColor: "#fff",
            borderRadius: "16px",
            boxShadow: "0 4px 16px rgba(0,0,0,0.1)",
            border: "1px solid #e9ecef",
          }}
        >
          <div
            style={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "flex-start",
              marginBottom: "20px",
            }}
          >
            <div>
              <h3
                style={{
                  margin: "0 0 10px 0",
                  color: "#2c3e50",
                  fontSize: "20px",
                  fontWeight: "600",
                }}
              >
                Developer Stats
              </h3>
              <div style={{ fontSize: "14px", color: "#495057" }}>
                <p style={{ margin: "8px 0", color: "#495057" }}>
                  📊 Widgets in catalog: {exampleWidgets.length}
                </p>
                <p style={{ margin: "8px 0", color: "#495057" }}>
                  🎯 Widgets in layout: {droppedWidgets.length}
                </p>
                <p style={{ margin: "8px 0", color: "#495057" }}>
                  📐 Current layout: {layoutType}
                </p>
                <p style={{ margin: "8px 0", color: "#495057" }}>
                  🔧 Grid columns: 12
                </p>
              </div>
            </div>
            <div>
              <h3
                style={{
                  margin: "0 0 10px 0",
                  color: "#2c3e50",
                  fontSize: "20px",
                  fontWeight: "600",
                }}
              >
                Quick Tips
              </h3>
              <div style={{ fontSize: "14px", color: "#495057" }}>
                <p style={{ margin: "8px 0", color: "#495057" }}>
                  💡 Use "Add Demo Widgets" to see examples
                </p>
                <p style={{ margin: "8px 0", color: "#495057" }}>
                  🎨 Try both Grid and Flex layouts
                </p>
                <p style={{ margin: "8px 0", color: "#495057" }}>
                  🔄 Use "Clear Layout" to start fresh
                </p>
                <p style={{ margin: "8px 0", color: "#495057" }}>
                  📱 Grid layout is responsive
                </p>
              </div>
            </div>
          </div>
        </footer>
      </div>
    </WidgetsProvider >
  );
}

export default App;
