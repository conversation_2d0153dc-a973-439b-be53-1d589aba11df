import { useState } from "react";
import { WidgetsProvider, WidgetsCatalog, Layout } from "./lib";
import type { Widget, LayoutType } from "./lib";
import type { Layout as RGLLayout } from "react-grid-layout";
import { CounterWidget } from "./example-widgets/CounterWidget";
import { GreetingWidget } from "./example-widgets/GreetingWidget";
import { TodoWidget } from "./example-widgets/TodoWidget";
import "./App.css";
import "react-grid-layout/css/styles.css";
import "react-resizable/css/styles.css";

// Create a widget registry to map component IDs to actual components
// This is necessary because React components cannot be serialized through JSON.stringify()
// during drag and drop operations, so we need to restore them from a registry
const widgetRegistry = {
  counter: CounterWidget,
  greeting: GreetingWidget,
  todo: TodoWidget,
};

// Helper function to get component from registry
const getWidgetComponent = (widgetId: string) => {
  return widgetRegistry[widgetId as keyof typeof widgetRegistry];
};

// Define example widgets
const exampleWidgets: Widget[] = [
  {
    id: "counter",
    name: "Counter Widget",
    description: "A simple counter that can increment and decrement values",
    component: CounterWidget,
    category: "Interactive",
    tags: ["counter", "interactive", "demo"],
    props: { initialValue: 0, step: 1 },
    layout: { w: 3, h: 4, x: 0, y: 0 },
  },
  {
    id: "greeting",
    name: "Greeting Widget",
    description: "A personalized greeting widget with name input",
    component: GreetingWidget,
    category: "Interactive",
    tags: ["greeting", "input", "demo"],
    props: { defaultName: "Developer" },
    layout: { w: 4, h: 3, x: 3, y: 0 },
  },
  {
    id: "todo",
    name: "Todo List Widget",
    description: "A simple todo list to manage tasks",
    component: TodoWidget,
    category: "Productivity",
    tags: ["todo", "list", "productivity", "tasks"],
    layout: { w: 5, h: 6, x: 7, y: 0 },
  },
];

function App() {
  const [droppedWidgets, setDroppedWidgets] = useState<Widget[]>([]);
  const [currentLayout, setCurrentLayout] = useState<RGLLayout[]>([]);


  const handleDrop = (layout: RGLLayout[], item: RGLLayout, e: Event) => {
    console.log("Drop event:", { layout, item, e });

    // Get the drag data from the event
    const dragEvent = e as DragEvent;
    const dragData = dragEvent.dataTransfer?.getData("application/json");

    if (dragData) {
      try {
        const data = JSON.parse(dragData);
        if (data.widget) {
          // Restore the component from the registry using the widget ID
          const component = getWidgetComponent(data.widget.id);

          if (component !== undefined) {
            const newWidget = {
              ...data.widget,
              component, // Restore the actual component
              id: `${data.widget.id}-${Date.now()}`, // Make unique ID for dropped instance
              layout: {
                ...data.widget.layout,
                x: item.x,
                y: item.y,
                w: Math.max(item.w, 4), // Minimum width of 4 grid units
                h: Math.max(item.h, 3), // Minimum height of 3 grid units
              },
            };

            // Only support grid layout now
            if (layoutType === "grid") {
              // For grid layout, add to dropped widgets
              setDroppedWidgets((prev) => [...prev, newWidget]);
            }
          } else {
            console.error(
              "Widget component not found in registry:",
              data.widget.id
            );
          }
        }
      } catch (error) {
        console.error("Error parsing drag data:", error);
      }
    }
  };

  const handleLayoutChange = (layout: RGLLayout[]) => {
    setCurrentLayout(layout);

    // Update the widget layout properties to match the new positions
    setDroppedWidgets(prevWidgets =>
      prevWidgets.map(widget => {
        const layoutItem = layout.find(item => item.i === widget.id);
        if (layoutItem) {
          return {
            ...widget,
            layout: {
              x: layoutItem.x,
              y: layoutItem.y,
              w: layoutItem.w,
              h: layoutItem.h
            }
          };
        }
        return widget;
      })
    );
  };

  const handleDrag = (widget: Widget) => {
    console.log("Dragging widget:", widget.name);
  };


  const renderDroppedWidgets = () => {
    return droppedWidgets.map((widget) => {
      const WidgetComponent = widget.component;

      // Find the current layout position for this widget
      const currentLayoutItem = currentLayout.find(item => item.i === widget.id);
      const gridData = currentLayoutItem || widget.layout;

      // Safety check to ensure component exists
      if (!WidgetComponent) {
        console.error("Widget component is undefined for widget:", widget.id);
        return (
          <div
            key={widget.id}
            data-grid={gridData}
            className="grid-widget-container"
          >
            <div className="grid-widget-content">
              <div
                className="widget-header"
                style={{
                  background:
                    "linear-gradient(135deg, #e74c3c 0%, #c0392b 100%)",
                }}
              >
                <div className="widget-title">
                  <span className="widget-icon">⚠️</span>
                  <span className="widget-name">{widget.name}</span>
                </div>
                <div className="widget-actions">
                  <button
                    className="widget-action-btn"
                    onClick={() => {
                      setDroppedWidgets((prev) =>
                        prev.filter((w) => w.id !== widget.id)
                      );
                      setCurrentLayout((prev) =>
                        prev.filter((l) => l.i !== widget.id)
                      );
                    }}
                    title="Remove widget"
                    aria-label="Remove widget"
                  >
                    ✕
                  </button>
                </div>
              </div>
              <div className="widget-body">
                <div
                  style={{
                    display: "flex",
                    flexDirection: "column",
                    alignItems: "center",
                    justifyContent: "center",
                    height: "100%",
                    color: "#e74c3c",
                    textAlign: "center",
                  }}
                >
                  <div style={{ fontSize: "32px", marginBottom: "12px" }}>
                    🚫
                  </div>
                  <div style={{ fontWeight: "bold", marginBottom: "8px" }}>
                    Component Error
                  </div>
                  <div style={{ fontSize: "14px", color: "#6c757d" }}>
                    Widget component not found
                  </div>
                </div>
              </div>
            </div>
          </div>
        );
      }

      return (
        <div
          key={widget.id}
          data-grid={gridData}
          className="grid-widget-container"
        >
          <div className="grid-widget-content">
            <div className="widget-header">
              <div className="widget-title drag-handle">
                <span className="widget-icon">🧩</span>
                <span className="widget-name">{widget.name}</span>
                <span className="drag-indicator" title="Drag to move">⋮⋮</span>
              </div>
              <div className="widget-actions">
                <button
                  className="widget-action-btn"
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    setDroppedWidgets((prev) =>
                      prev.filter((w) => w.id !== widget.id)
                    );
                    setCurrentLayout((prev) =>
                      prev.filter((l) => l.i !== widget.id)
                    );
                  }}
                  onMouseDown={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                  }}
                  title="Remove widget"
                  aria-label="Remove widget"
                  style={{
                    pointerEvents: 'auto',
                    zIndex: 1000,
                    position: 'relative'
                  }}
                >
                  ✕
                </button>
              </div>
            </div>
            <div
              className="widget-body"
              onMouseDown={(e) => {
                // Only prevent dragging if clicking on interactive elements
                const target = e.target as HTMLElement;
                if (target.tagName === 'BUTTON' || target.tagName === 'INPUT' || target.tagName === 'SELECT' || target.tagName === 'TEXTAREA') {
                  e.stopPropagation();
                }
              }}
            >
                <WidgetComponent key={`widget-${widget.id}`} {...(widget.props || {})} />
            </div>

          </div>
        </div>
      );
    });
  };


  return (
    <WidgetsProvider initialWidgets={exampleWidgets}>
      <div
        style={{
          minHeight: "100vh",
          width: "100%",
        }}
      >
        <header
          style={{
            textAlign: "center",
            marginBottom: "40px",
            padding: "40px 20px",
            backgroundColor: "#fff",
            borderRadius: "16px",
            boxShadow: "0 4px 16px rgba(0,0,0,0.1)",
            background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
            color: "white",
          }}
        >
          <h1
            style={{
              margin: "0 0 16px 0",
              fontSize: "48px",
              fontWeight: "700",
              textShadow: "0 2px 4px rgba(0,0,0,0.3)",
            }}
          >
            🚀 Tech4Fab Widget Package
          </h1>
          <p
            style={{
              fontSize: "20px",
              opacity: 0.9,
              lineHeight: "1.5",
              maxWidth: "600px",
              margin: "0 auto",
            }}
          >
            Interactive demonstration of WidgetsProvider, WidgetsCatalog, and
            dual Layout components with drag & drop functionality
          </p>
        </header>

        <div style={{ display: "flex", gap: "24px", minHeight: "1000px" }}>
          {/* Widget Catalog */}
          <div
            style={{
              width: "350px",
              flexShrink: 0,
              backgroundColor: "#fff",
              border: "1px solid #e0e0e0",
              borderRadius: "12px",
              padding: "20px",
              boxShadow: "0 2px 8px rgba(0,0,0,0.1)",
              height: "fit-content",
            }}
          >
            <div style={{ marginBottom: "16px" }}>
              <h3
                style={{
                  margin: "0 0 8px 0",
                  fontSize: "20px",
                  fontWeight: "600",
                  color: "#2c3e50",
                }}
              >
                🧩 Widget Catalog
              </h3>
              <p
                style={{
                  fontSize: "14px",
                  color: "#7f8c8d",
                  margin: 0,
                  lineHeight: "1.4",
                }}
              >
                Drag widgets to the layout area to add them →
              </p>
            </div>
            <WidgetsCatalog
              widgets={exampleWidgets}
              onDrag={handleDrag}
              enableDrag={true}
              showSearch={true}
              showCategories={true}
            />
          </div>

          {/* Layout Area */}
          <div
            style={{
              flex: 1,
              border: "2px dashed #3498db",
              borderRadius: "16px",
              minHeight: "900px",
              display: "flex",
              flexDirection: "column",
              backgroundColor: "#fff",
              boxShadow: "0 4px 16px rgba(0,0,0,0.1)",
              overflow: "hidden",
            }}
          >
            <div
              style={{
                flex: 1,
                display: "flex",
                flexDirection: "column",
                minHeight: "800px",
                padding: "20px",
                backgroundColor: "#fafbfc",
                overflow: "hidden",
              }}
            >
              <div className="layout-container">
                <Layout
                  onDrop={handleDrop}
                  onLayoutChange={handleLayoutChange}
                  layout={currentLayout}
                  cols={12}
                  rowHeight={50} // Even larger row height for better visibility
                  width={1400} // Increased width for more space
                  margin={[16, 16]} // Larger margins
                  containerPadding={[24, 24]} // More padding
                  isDraggable={true}
                  isResizable={true}
                  isDroppable={true}
                >
                  {
                    droppedWidgets.length === 0 ? (
                      <div style={{
                        display: 'flex',
                        flexDirection: 'column',
                        alignItems: 'center',
                        justifyContent: 'center',
                        height: '100%',
                        color: '#888',
                        fontSize: 20,
                        opacity: 0.7
                      }}>
                        <div style={{ fontSize: 48, marginBottom: 12 }}>🧩</div>
                        <div>Drop widgets here to get started!</div>
                      </div>
                    ) : renderDroppedWidgets()
                  }
                </Layout>
              </div>
            </div>
          </div>
        </div>
      </div>
    </WidgetsProvider >
  );
}

export default App;
