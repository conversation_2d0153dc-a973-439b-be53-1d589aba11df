import React from 'react';
import type { Layout as RGLLayout } from 'react-grid-layout';

export interface Widget {
  id: string;
  name: string;
  description?: string;
  component: React.ComponentType<Record<string, unknown>>;
  props?: Record<string, unknown>;
  category?: string;
  tags?: string[];
  // Layout properties for grid layout
  layout?: {
    x?: number;
    y?: number;
    w?: number;
    h?: number;
    minW?: number;
    minH?: number;
    maxW?: number;
    maxH?: number;
    static?: boolean;
    isDraggable?: boolean;
    isResizable?: boolean;
  };
}

export interface WidgetCategory {
  id: string;
  name: string;
  description?: string;
  widgets: Widget[];
}

export interface WidgetsContextType {
  widgets: Widget[];
  categories: WidgetCategory[];
  addWidget: (widget: Widget) => void;
  removeWidget: (widgetId: string) => void;
  getWidgetsByCategory: (categoryId: string) => Widget[];
  searchWidgets: (query: string) => Widget[];
}

export interface WidgetsProviderProps {
  children: React.ReactNode;
  initialWidgets?: Widget[];
  initialCategories?: WidgetCategory[];
}

export interface WidgetsCatalogProps {
  className?: string;
  showSearch?: boolean;
  showCategories?: boolean;
  widgets?: Widget[];
  onWidgetSelect?: (widget: Widget) => void;
  onDrag?: (widget: Widget) => void;
  renderWidget?: (widget: Widget) => React.ReactNode;
  enableDrag?: boolean;
  // NEW: allow developer to customize search input
  renderSearch?: (props: {
    value: string;
    onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  }) => React.ReactNode;
  // NEW: allow developer to customize category select
  renderCategorySelect?: (props: {
    value: string;
    options: string[];
    onChange: (e: React.ChangeEvent<HTMLSelectElement>) => void;
  }) => React.ReactNode;
}

export type LayoutType = 'grid';

export interface LayoutProps {
  type: LayoutType;
  children?: React.ReactNode;
  className?: string;
  // Grid layout specific props
  onDrop?: (layout: RGLLayout[], item: RGLLayout, e: Event) => void;
  onLayoutChange?: (layout: RGLLayout[]) => void;
  layout?: RGLLayout[];
  cols?: number;
  rowHeight?: number;
  width?: number;
  margin?: [number, number];
  containerPadding?: [number, number];
  isDraggable?: boolean;
  isResizable?: boolean;
  isDroppable?: boolean;
  droppingItem?: Partial<RGLLayout>; // NEW: allow customizing droppingItem
  breakpoints?: { [key: string]: number }; // NEW: allow customizing breakpoints
  colsByBreakpoint?: { [key: string]: number }; // NEW: allow customizing cols per breakpoint
  // NEW: custom className and style for inner layout
  layoutClassName?: string;
  layoutStyle?: React.CSSProperties;
}

export interface DragItem {
  widget: Widget;
  type: string;
}
