import type { Widget, WidgetCategory } from './types';
import type { Layout as RGLLayout } from 'react-grid-layout';

/**
 * Utility functions to help developers integrate widgets more easily
 */

/**
 * Creates a widget with sensible defaults
 */
export function createWidget(
  id: string,
  name: string,
  component: React.ComponentType<any>,
  options: Partial<Omit<Widget, 'id' | 'name' | 'component'>> = {}
): Widget {
  return {
    id,
    name,
    component,
    description: options.description || `${name} widget`,
    category: options.category || 'General',
    tags: options.tags || [id.toLowerCase()],
    props: options.props || {},
    layout: {
      w: 4,
      h: 3,
      x: 0,
      y: 0,
      minW: 2,
      minH: 2,
      ...options.layout
    }
  };
}

/**
 * Creates a widget category with widgets
 */
export function createCategory(
  id: string,
  name: string,
  widgets: Widget[],
  description?: string
): WidgetCategory {
  return {
    id,
    name,
    description: description || `${name} category`,
    widgets
  };
}

/**
 * Generates a unique widget ID for dropped instances
 */
export function generateWidgetInstanceId(baseId: string): string {
  return `${baseId}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * Validates widget configuration
 */
export function validateWidget(widget: Widget): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];

  if (!widget.id || typeof widget.id !== 'string') {
    errors.push('Widget must have a valid string ID');
  }

  if (!widget.name || typeof widget.name !== 'string') {
    errors.push('Widget must have a valid string name');
  }

  if (!widget.component || typeof widget.component !== 'function') {
    errors.push('Widget must have a valid React component');
  }

  if (widget.layout) {
    if (widget.layout.w !== undefined && (widget.layout.w < 1 || widget.layout.w > 12)) {
      errors.push('Widget width must be between 1 and 12');
    }
    if (widget.layout.h !== undefined && widget.layout.h < 1) {
      errors.push('Widget height must be at least 1');
    }
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Converts widget layout to react-grid-layout format
 */
export function widgetToGridLayout(widget: Widget): RGLLayout {
  return {
    i: widget.id,
    x: widget.layout?.x || 0,
    y: widget.layout?.y || 0,
    w: widget.layout?.w || 4,
    h: widget.layout?.h || 3,
    minW: widget.layout?.minW || 2,
    minH: widget.layout?.minH || 2,
    maxW: widget.layout?.maxW,
    maxH: widget.layout?.maxH,
    static: widget.layout?.static || false,
    isDraggable: widget.layout?.isDraggable !== false,
    isResizable: widget.layout?.isResizable !== false
  };
}

/**
 * Converts react-grid-layout to widget layout format
 */
export function gridLayoutToWidget(layout: RGLLayout, widget: Widget): Widget {
  return {
    ...widget,
    layout: {
      ...widget.layout,
      x: layout.x,
      y: layout.y,
      w: layout.w,
      h: layout.h
    }
  };
}

/**
 * Filters widgets by search query
 */
export function filterWidgets(widgets: Widget[], query: string): Widget[] {
  if (!query.trim()) return widgets;

  const searchTerm = query.toLowerCase().trim();
  
  return widgets.filter(widget => 
    widget.name.toLowerCase().includes(searchTerm) ||
    widget.description?.toLowerCase().includes(searchTerm) ||
    widget.category?.toLowerCase().includes(searchTerm) ||
    widget.tags?.some(tag => tag.toLowerCase().includes(searchTerm))
  );
}

/**
 * Groups widgets by category
 */
export function groupWidgetsByCategory(widgets: Widget[]): Record<string, Widget[]> {
  return widgets.reduce((groups, widget) => {
    const category = widget.category || 'Uncategorized';
    if (!groups[category]) {
      groups[category] = [];
    }
    groups[category].push(widget);
    return groups;
  }, {} as Record<string, Widget[]>);
}

/**
 * Finds optimal position for a new widget in the grid
 */
export function findOptimalPosition(
  existingLayouts: RGLLayout[],
  newWidget: Widget,
  cols: number = 12
): { x: number; y: number } {
  const width = newWidget.layout?.w || 4;
  const height = newWidget.layout?.h || 3;

  // Try to find a position in the first row
  for (let x = 0; x <= cols - width; x++) {
    if (!hasCollision(existingLayouts, { x, y: 0, w: width, h: height })) {
      return { x, y: 0 };
    }
  }

  // Find the lowest available position
  let y = 0;
  while (true) {
    for (let x = 0; x <= cols - width; x++) {
      if (!hasCollision(existingLayouts, { x, y, w: width, h: height })) {
        return { x, y };
      }
    }
    y++;
  }
}

/**
 * Checks if a layout position collides with existing layouts
 */
function hasCollision(
  layouts: RGLLayout[],
  testLayout: { x: number; y: number; w: number; h: number }
): boolean {
  return layouts.some(layout => 
    layout.x < testLayout.x + testLayout.w &&
    layout.x + layout.w > testLayout.x &&
    layout.y < testLayout.y + testLayout.h &&
    layout.y + layout.h > testLayout.y
  );
}

/**
 * Creates default grid layout configuration
 */
export function createDefaultGridConfig() {
  return {
    cols: 12,
    rowHeight: 60,
    width: 1200,
    margin: [16, 16] as [number, number],
    containerPadding: [16, 16] as [number, number],
    isDraggable: true,
    isResizable: true,
    isDroppable: true
  };
}

/**
 * Widget registry for managing component references during serialization
 */
export class WidgetRegistry {
  private registry = new Map<string, React.ComponentType<any>>();

  register(id: string, component: React.ComponentType<any>): void {
    this.registry.set(id, component);
  }

  get(id: string): React.ComponentType<any> | undefined {
    return this.registry.get(id);
  }

  has(id: string): boolean {
    return this.registry.has(id);
  }

  unregister(id: string): boolean {
    return this.registry.delete(id);
  }

  clear(): void {
    this.registry.clear();
  }

  getAll(): Map<string, React.ComponentType<any>> {
    return new Map(this.registry);
  }
}

/**
 * Default widget registry instance
 */
export const defaultWidgetRegistry = new WidgetRegistry();

/**
 * Serializes widget for drag and drop (excludes component)
 */
export function serializeWidget(widget: Widget): string {
  const { component, ...serializable } = widget;
  return JSON.stringify({ widget: serializable });
}

/**
 * Deserializes widget and restores component from registry
 */
export function deserializeWidget(
  data: string,
  registry: WidgetRegistry = defaultWidgetRegistry
): Widget | null {
  try {
    const parsed = JSON.parse(data);
    if (parsed.widget && registry.has(parsed.widget.id)) {
      return {
        ...parsed.widget,
        component: registry.get(parsed.widget.id)!
      };
    }
  } catch (error) {
    console.error('Failed to deserialize widget:', error);
  }
  return null;
}
