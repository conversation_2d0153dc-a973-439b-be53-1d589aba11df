import { Component } from 'react';
import type { ReactNode } from 'react';

interface WidgetErrorBoundaryProps {
    children: ReactNode;
    widgetName?: string;
}

interface WidgetErrorBoundaryState {
    hasError: boolean;
    error?: Error;
}

export class WidgetErrorBoundary extends Component<WidgetErrorBoundaryProps, WidgetErrorBoundaryState> {
    constructor(props: WidgetErrorBoundaryProps) {
        super(props);
        this.state = { hasError: false };
    }

    static getDerivedStateFromError(error: Error) {
        return { hasError: true, error };
    }

    componentDidCatch() {
        // Optionally log error info here
    }

    render() {
        if (this.state.hasError) {
            return (
                <div style={{ color: '#e74c3c', padding: 16, textAlign: 'center', background: '#fff0f0', borderRadius: 8 }}>
                    <div style={{ fontSize: 32, marginBottom: 8 }}>🚫</div>
                    <div style={{ fontWeight: 'bold', marginBottom: 4 }}>Widget Error</div>
                    <div style={{ fontSize: 14, color: '#6c757d' }}>
                        {this.props.widgetName ? `An error occurred in "${this.props.widgetName}" widget.` : 'An error occurred in this widget.'}
                    </div>
                </div>
            );
        }
        return this.props.children;
    }
}
