// Main exports
export { WidgetsProvider, useWidgets } from './WidgetsProvider';
export { WidgetsCatalog } from './WidgetsCatalog';
export { Layout } from './Layout';

// Utility exports
export {
  createWidget,
  createCategory,
  generateWidgetInstanceId,
  validateWidget,
  widgetToGridLayout,
  gridLayoutToWidget,
  filterWidgets,
  groupWidgetsByCategory,
  findOptimalPosition,
  createDefaultGridConfig,
  WidgetRegistry,
  defaultWidgetRegistry,
  serializeWidget,
  deserializeWidget,
} from './utils';



// Type exports
export type {
  Widget,
  WidgetCategory,
  WidgetsContextType,
  WidgetsProviderProps,
  WidgetsCatalogProps,
  LayoutProps,
  LayoutType,
  DragItem,
} from './types';

// Default export for convenience
export { WidgetsProvider as default } from './WidgetsProvider';
