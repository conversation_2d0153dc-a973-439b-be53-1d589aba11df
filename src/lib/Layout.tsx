import React, { useMemo } from 'react';
import { Responsive, WidthProvider } from 'react-grid-layout';
import type { Layout as RGLLayout } from 'react-grid-layout';
import { Layout as FlexLayout } from 'flexlayout-react';
import type { LayoutProps } from './types';

const ResponsiveGridLayout = WidthProvider(Responsive);

export const Layout: React.FC<LayoutProps> = ({
  type,
  children,
  className = '',
  // Grid layout props
  onDrop,
  onLayoutChange,
  layout = [],
  cols = 12,
  rowHeight = 30,
  margin = [10, 10],
  containerPadding = [10, 10],
  isDraggable = true,
  isResizable = true,
  isDroppable = true,
  droppingItem = { i: '__dropping-elem__', w: 4, h: 5 }, // allow override
  breakpoints = { lg: 1200, md: 996, sm: 768, xs: 480, xxs: 0 },
  colsByBreakpoint = { lg: cols, md: 10, sm: 6, xs: 4, xxs: 2 },
  layoutClassName = '',
  layoutStyle = {},
  droppingItemFlex = { i: 'new-item', w: 2, h: 2 }, // NEW: allow override for flex drop
  // Flex layout props (flexlayout-react)
  flexModel,
  flexFactory,
  onFlexModelChange,
  // Simple flex layout props (CSS flexbox fallback)
  flexDirection = 'row',
  justifyContent = 'flex-start',
  alignItems = 'stretch',
  flexWrap = 'wrap',
  gap = '16px',
}) => {
  const flexStyles = useMemo(() => ({
    display: 'flex',
    flexDirection,
    justifyContent,
    alignItems,
    flexWrap,
    gap,
    width: '100%',
    height: '100%',
    padding: '10px',
  }), [flexDirection, justifyContent, alignItems, flexWrap, gap]);

  if (type === 'grid') {
    // Ensure droppingItem always has required fields
    const safeDroppingItem = {
      i: '__dropping-elem__',
      w: 4,
      h: 5,
      ...droppingItem,
    };
    return (
      <div className={`layout-grid ${className}`} style={layoutStyle}>
        <ResponsiveGridLayout
          className={`layout ${layoutClassName}`}
          layouts={{ lg: layout }}
          breakpoints={breakpoints}
          cols={colsByBreakpoint}
          rowHeight={rowHeight}
          margin={margin}
          containerPadding={containerPadding}
          isDraggable={isDraggable}
          isResizable={isResizable}
          isDroppable={isDroppable}
          onLayoutChange={(layout: RGLLayout[]) => {
            if (onLayoutChange) {
              onLayoutChange(layout);
            }
          }}
          onDrop={(layout: RGLLayout[], layoutItem: RGLLayout, event: Event) => {
            if (onDrop) {
              onDrop(layout, layoutItem, event);
            }
          }}
          droppingItem={safeDroppingItem}
        >
          {children}
        </ResponsiveGridLayout>
      </div>
    );
  }

  if (type === 'flex') {
    // If flexModel and flexFactory are provided, use FlexLayout
    if (flexModel && flexFactory) {
      return (
        <div
          className={`layout-flexlayout ${className} ${layoutClassName}`}
          style={{
            height: '100%',
            minHeight: '400px',
            width: '100%',
            position: 'relative',
            ...layoutStyle
          }}
          onDrop={(e) => {
            e.preventDefault();
            // Handle flex layout drop
            const dragData = e.dataTransfer.getData('application/json');
            if (dragData && onDrop) {
              try {
                const data = JSON.parse(dragData);
                // Create a mock layout item for flex layout
                const mockLayoutItem: RGLLayout = {
                  i: data.widget?.id || droppingItemFlex.i || 'new-item',
                  x: droppingItemFlex.x ?? 0,
                  y: droppingItemFlex.y ?? 0,
                  w: droppingItemFlex.w ?? 2,
                  h: droppingItemFlex.h ?? 2,
                  minW: droppingItemFlex.minW,
                  maxW: droppingItemFlex.maxW,
                  minH: droppingItemFlex.minH,
                  maxH: droppingItemFlex.maxH,
                  static: droppingItemFlex.static,
                  isDraggable: droppingItemFlex.isDraggable,
                  isResizable: droppingItemFlex.isResizable,
                  moved: droppingItemFlex.moved,
                  isBounded: droppingItemFlex.isBounded,
                };
                onDrop([], mockLayoutItem, e.nativeEvent);
              } catch (error) {
                console.error('Error parsing drag data:', error);
              }
            }
          }}
          onDragOver={(e) => {
            e.preventDefault();
            e.dataTransfer.dropEffect = 'copy';
          }}
        >
          <FlexLayout
            model={flexModel}
            factory={flexFactory}
            onModelChange={(newModel) => {
              console.log('FlexLayout onModelChange:', newModel);
              if (onFlexModelChange && newModel && typeof newModel.getRoot === 'function') {
                onFlexModelChange(newModel);
              }
            }}
          />
        </div>
      );
    }

    // Fallback to simple CSS flexbox layout
    return (
      <div
        className={`layout-flex ${className} ${layoutClassName}`}
        style={{ ...flexStyles, ...layoutStyle }}
        onDrop={(e) => {
          e.preventDefault();
          // Handle flex layout drop
          const dragData = e.dataTransfer.getData('application/json');
          if (dragData && onDrop) {
            try {
              const data = JSON.parse(dragData);
              // Create a mock layout item for flex layout
              const mockLayoutItem: RGLLayout = {
                ...droppingItemFlex,
                i: data.widget?.id || droppingItemFlex.i || 'new-item',
                x: droppingItemFlex.x ?? 0,
                y: droppingItemFlex.y ?? 0,
                w: droppingItemFlex.w ?? 2,
                h: droppingItemFlex.h ?? 2,
                minW: droppingItemFlex.minW,
                maxW: droppingItemFlex.maxW,
                minH: droppingItemFlex.minH,
                maxH: droppingItemFlex.maxH,
                static: droppingItemFlex.static,
                isDraggable: droppingItemFlex.isDraggable,
                isResizable: droppingItemFlex.isResizable,
                moved: droppingItemFlex.moved,
                isBounded: droppingItemFlex.isBounded,
              };
              onDrop([], mockLayoutItem, e.nativeEvent);
            } catch (error) {
              console.error('Error parsing drag data:', error);
            }
          }
        }}
        onDragOver={(e) => {
          e.preventDefault();
        }}
      >
        {React.Children.count(children) === 0 ? (
          <div style={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            height: '100%',
            color: '#888',
            fontSize: 20,
            opacity: 0.7
          }}>
            <div style={{ fontSize: 48, marginBottom: 12 }}>🧩</div>
            <div>Drop widgets here to get started!</div>
          </div>
        ) : (
          React.Children.map(children, child =>
            child ? (
              // @ts-expect-error: WidgetErrorBoundary may not match all children types
              <WidgetErrorBoundary>{child}</WidgetErrorBoundary>
            ) : null
          )
        )}
      </div>
    );
  }

  return (
    <div className={`layout-unknown ${className}`}>
      <div style={{ padding: '20px', textAlign: 'center', color: '#666' }}>
        Unknown layout type: {type}
      </div>
    </div>
  );
};

export default Layout;
