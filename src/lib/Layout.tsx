import React from 'react';
import { Responsive, WidthProvider } from 'react-grid-layout';
import type { Layout as RGLLayout } from 'react-grid-layout';
import type { LayoutProps } from './types';

const ResponsiveGridLayout = WidthProvider(Responsive);

export const Layout: React.FC<LayoutProps> = ({
  type,
  children,
  className = '',
  // Grid layout props
  onDrop,
  onLayoutChange,
  layout = [],
  cols = 12,
  rowHeight = 30,
  margin = [10, 10],
  containerPadding = [10, 10],
  isDraggable = true,
  isResizable = true,
  isDroppable = true,
  droppingItem = { i: '__dropping-elem__', w: 4, h: 5 }, // allow override
  breakpoints = { lg: 1200, md: 996, sm: 768, xs: 480, xxs: 0 },
  colsByBreakpoint = { lg: cols, md: 10, sm: 6, xs: 4, xxs: 2 },
  layoutClassName = '',
  layoutStyle = {},
}) => {
  // Only support grid layout now
  if (type === 'grid') {
    // Ensure droppingItem always has required fields
    const safeDroppingItem = {
      i: '__dropping-elem__',
      w: 4,
      h: 5,
      ...droppingItem,
    };
    return (
      <div className={`layout-grid ${className}`} style={layoutStyle}>
        <ResponsiveGridLayout
          className={`layout ${layoutClassName}`}
          layouts={{ lg: layout }}
          breakpoints={breakpoints}
          cols={colsByBreakpoint}
          rowHeight={rowHeight}
          margin={margin}
          containerPadding={containerPadding}
          isDraggable={isDraggable}
          isResizable={isResizable}
          isDroppable={isDroppable}
          onLayoutChange={(layout: RGLLayout[]) => {
            if (onLayoutChange) {
              onLayoutChange(layout);
            }
          }}
          onDrop={(layout: RGLLayout[], layoutItem: RGLLayout, event: Event) => {
            if (onDrop) {
              onDrop(layout, layoutItem, event);
            }
          }}
          droppingItem={safeDroppingItem}
        >
          {children}
        </ResponsiveGridLayout>
      </div>
    );
  }

  // Fallback for unsupported layout types
  return (
    <div className={`layout-unsupported ${className}`} style={layoutStyle}>
      <div style={{
        padding: '20px',
        textAlign: 'center',
        color: '#666',
        border: '2px dashed #ddd',
        borderRadius: '8px'
      }}>
        <h3>Unsupported Layout Type</h3>
        <p>Only 'grid' layout is supported. Current type: '{type}'</p>
      </div>
    </div>
  );
};
