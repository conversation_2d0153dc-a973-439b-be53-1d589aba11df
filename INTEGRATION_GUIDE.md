# 🚀 Widget Package Integration Guide

This comprehensive guide will help you integrate the Widget Package into your React application step by step.

## 📋 Table of Contents

1. [Prerequisites](#prerequisites)
2. [Installation](#installation)
3. [Basic Setup](#basic-setup)
4. [Creating Your First Widget](#creating-your-first-widget)
5. [Advanced Widget Patterns](#advanced-widget-patterns)
6. [Layout Integration](#layout-integration)
7. [Customization](#customization)
8. [Best Practices](#best-practices)
9. [Troubleshooting](#troubleshooting)

## Prerequisites

Before you begin, ensure you have:

- React 18.0.0 or higher
- TypeScript (recommended but not required)
- A bundler that supports CSS imports (Vite, Webpack, etc.)

## Installation

### 1. Install the Package

```bash
npm install widget-package react-grid-layout react-resizable flexlayout-react
```

### 2. Import Required CSS

Add these imports to your main CSS file or application entry point:

```css
/* Required for grid layout */
@import 'react-grid-layout/css/styles.css';
@import 'react-resizable/css/styles.css';

/* Required for flex layout */
@import 'flexlayout-react/style/light.css';
```

Or in your JavaScript/TypeScript files:

```tsx
import 'react-grid-layout/css/styles.css';
import 'react-resizable/css/styles.css';
import 'flexlayout-react/style/light.css';
```

## Basic Setup

### 1. Wrap Your App with WidgetsProvider

```tsx
// App.tsx
import React from 'react';
import { WidgetsProvider } from 'widget-package';
import Dashboard from './Dashboard';

function App() {
  return (
    <WidgetsProvider>
      <Dashboard />
    </WidgetsProvider>
  );
}

export default App;
```

### 2. Create a Simple Widget Catalog

```tsx
// Dashboard.tsx
import React from 'react';
import { WidgetsCatalog } from 'widget-package';

function Dashboard() {
  return (
    <div style={{ padding: '20px' }}>
      <h1>My Dashboard</h1>
      <WidgetsCatalog
        showSearch={true}
        showCategories={true}
        onWidgetSelect={(widget) => console.log('Selected:', widget)}
      />
    </div>
  );
}

export default Dashboard;
```

## Creating Your First Widget

### 1. Define a Widget Component

```tsx
// widgets/StatusWidget.tsx
import React from 'react';

interface StatusWidgetProps {
  title?: string;
  status?: 'online' | 'offline' | 'maintenance';
  count?: number;
}

export const StatusWidget: React.FC<StatusWidgetProps> = ({
  title = 'System Status',
  status = 'online',
  count = 0
}) => {
  const getStatusColor = () => {
    switch (status) {
      case 'online': return '#27ae60';
      case 'offline': return '#e74c3c';
      case 'maintenance': return '#f39c12';
      default: return '#95a5a6';
    }
  };

  const getStatusIcon = () => {
    switch (status) {
      case 'online': return '✅';
      case 'offline': return '❌';
      case 'maintenance': return '🔧';
      default: return '❓';
    }
  };

  return (
    <div style={{
      padding: '16px',
      backgroundColor: '#fff',
      borderRadius: '8px',
      border: '1px solid #e1e8ed',
      height: '100%',
      display: 'flex',
      flexDirection: 'column'
    }}>
      <h3 style={{ margin: '0 0 12px 0', fontSize: '16px' }}>{title}</h3>
      
      <div style={{ 
        flex: 1, 
        display: 'flex', 
        alignItems: 'center', 
        justifyContent: 'center',
        flexDirection: 'column'
      }}>
        <div style={{ fontSize: '32px', marginBottom: '8px' }}>
          {getStatusIcon()}
        </div>
        
        <div style={{
          fontSize: '18px',
          fontWeight: 'bold',
          color: getStatusColor(),
          textTransform: 'capitalize',
          marginBottom: '4px'
        }}>
          {status}
        </div>
        
        {count > 0 && (
          <div style={{ fontSize: '14px', color: '#666' }}>
            {count} active connections
          </div>
        )}
      </div>
    </div>
  );
};
```

### 2. Register Your Widget

```tsx
// widgets/index.ts
import { createWidget } from 'widget-package';
import { StatusWidget } from './StatusWidget';

export const statusWidget = createWidget(
  'status-widget',
  'System Status',
  StatusWidget,
  {
    description: 'Shows the current system status',
    category: 'Monitoring',
    tags: ['status', 'monitoring', 'system'],
    props: {
      title: 'Server Status',
      status: 'online',
      count: 42
    },
    layout: {
      w: 3,
      h: 3,
      minW: 2,
      minH: 2
    }
  }
);
```

### 3. Add Widget to Your Catalog

```tsx
// Dashboard.tsx
import React from 'react';
import { WidgetsProvider, WidgetsCatalog } from 'widget-package';
import { statusWidget } from './widgets';

const widgets = [statusWidget];

function Dashboard() {
  return (
    <WidgetsProvider initialWidgets={widgets}>
      <div style={{ padding: '20px' }}>
        <h1>My Dashboard</h1>
        <WidgetsCatalog
          showSearch={true}
          showCategories={true}
          onWidgetSelect={(widget) => console.log('Selected:', widget)}
        />
      </div>
    </WidgetsProvider>
  );
}

export default Dashboard;
```

## Advanced Widget Patterns

### 1. Widget with State Management

```tsx
// widgets/CounterWidget.tsx
import React, { useState, useEffect } from 'react';

interface CounterWidgetProps {
  initialValue?: number;
  step?: number;
  autoIncrement?: boolean;
  interval?: number;
}

export const CounterWidget: React.FC<CounterWidgetProps> = ({
  initialValue = 0,
  step = 1,
  autoIncrement = false,
  interval = 1000
}) => {
  const [count, setCount] = useState(initialValue);

  useEffect(() => {
    if (!autoIncrement) return;

    const timer = setInterval(() => {
      setCount(prev => prev + step);
    }, interval);

    return () => clearInterval(timer);
  }, [autoIncrement, step, interval]);

  return (
    <div style={{ padding: '16px', textAlign: 'center', height: '100%' }}>
      <h3>Counter Widget</h3>
      <div style={{ fontSize: '48px', margin: '20px 0' }}>{count}</div>
      <div style={{ display: 'flex', gap: '8px', justifyContent: 'center' }}>
        <button onClick={() => setCount(count - step)}>-{step}</button>
        <button onClick={() => setCount(initialValue)}>Reset</button>
        <button onClick={() => setCount(count + step)}>+{step}</button>
      </div>
    </div>
  );
};
```

### 2. Widget with External Data

```tsx
// widgets/ApiWidget.tsx
import React, { useState, useEffect } from 'react';

interface ApiWidgetProps {
  endpoint?: string;
  refreshInterval?: number;
  title?: string;
}

export const ApiWidget: React.FC<ApiWidgetProps> = ({
  endpoint = '/api/data',
  refreshInterval = 30000,
  title = 'API Data'
}) => {
  const [data, setData] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchData = async () => {
    try {
      setLoading(true);
      setError(null);
      
      // Replace with your actual API call
      const response = await fetch(endpoint);
      if (!response.ok) throw new Error('Failed to fetch');
      
      const result = await response.json();
      setData(result);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
    
    if (refreshInterval > 0) {
      const timer = setInterval(fetchData, refreshInterval);
      return () => clearInterval(timer);
    }
  }, [endpoint, refreshInterval]);

  if (loading) return <div style={{ padding: '16px' }}>Loading...</div>;
  if (error) return <div style={{ padding: '16px', color: 'red' }}>Error: {error}</div>;

  return (
    <div style={{ padding: '16px', height: '100%' }}>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <h3>{title}</h3>
        <button onClick={fetchData} style={{ padding: '4px 8px' }}>
          🔄 Refresh
        </button>
      </div>
      <pre style={{ fontSize: '12px', overflow: 'auto' }}>
        {JSON.stringify(data, null, 2)}
      </pre>
    </div>
  );
};
```

## Layout Integration

### 1. Grid Layout with Drag & Drop

```tsx
// components/GridDashboard.tsx
import React, { useState } from 'react';
import { 
  WidgetsProvider, 
  WidgetsCatalog, 
  Layout,
  generateWidgetInstanceId 
} from 'widget-package';
import type { Widget } from 'widget-package';
import type { Layout as RGLLayout } from 'react-grid-layout';

interface GridDashboardProps {
  widgets: Widget[];
}

export const GridDashboard: React.FC<GridDashboardProps> = ({ widgets }) => {
  const [droppedWidgets, setDroppedWidgets] = useState<Widget[]>([]);
  const [currentLayout, setCurrentLayout] = useState<RGLLayout[]>([]);

  const handleDrop = (layout: RGLLayout[], item: RGLLayout, e: Event) => {
    const dragEvent = e as DragEvent;
    const dragData = dragEvent.dataTransfer?.getData('application/json');
    
    if (dragData) {
      const data = JSON.parse(dragData);
      const newWidget = {
        ...data.widget,
        id: generateWidgetInstanceId(data.widget.id),
        layout: { x: item.x, y: item.y, w: item.w, h: item.h }
      };
      setDroppedWidgets(prev => [...prev, newWidget]);
    }
  };

  const handleLayoutChange = (layout: RGLLayout[]) => {
    setCurrentLayout(layout);
  };

  const removeWidget = (widgetId: string) => {
    setDroppedWidgets(prev => prev.filter(w => w.id !== widgetId));
    setCurrentLayout(prev => prev.filter(l => l.i !== widgetId));
  };

  return (
    <WidgetsProvider initialWidgets={widgets}>
      <div style={{ display: 'flex', height: '100vh' }}>
        <div style={{ width: '300px', padding: '20px', borderRight: '1px solid #ddd' }}>
          <h2>Widgets</h2>
          <WidgetsCatalog enableDrag={true} showSearch={true} />
        </div>
        
        <div style={{ flex: 1, padding: '20px' }}>
          <h2>Dashboard</h2>
          <Layout
            type="grid"
            onDrop={handleDrop}
            onLayoutChange={handleLayoutChange}
            layout={currentLayout}
            cols={12}
            rowHeight={60}
            isDraggable={true}
            isResizable={true}
            isDroppable={true}
          >
            {droppedWidgets.map((widget) => {
              const WidgetComponent = widget.component;
              return (
                <div key={widget.id} data-grid={widget.layout}>
                  <div style={{ position: 'relative', height: '100%' }}>
                    <button
                      onClick={() => removeWidget(widget.id)}
                      style={{
                        position: 'absolute',
                        top: '4px',
                        right: '4px',
                        zIndex: 1000,
                        background: '#e74c3c',
                        color: 'white',
                        border: 'none',
                        borderRadius: '50%',
                        width: '20px',
                        height: '20px',
                        cursor: 'pointer'
                      }}
                    >
                      ×
                    </button>
                    <WidgetComponent {...(widget.props || {})} />
                  </div>
                </div>
              );
            })}
          </Layout>
        </div>
      </div>
    </WidgetsProvider>
  );
};
```

## Best Practices

### 1. Widget Design Guidelines

- **Keep widgets focused**: Each widget should have a single, clear purpose
- **Make them responsive**: Design widgets to work well at different sizes
- **Handle loading states**: Always show loading indicators for async operations
- **Error handling**: Gracefully handle and display errors
- **Accessibility**: Include proper ARIA labels and keyboard navigation

### 2. Performance Optimization

```tsx
// Use React.memo for widgets that don't change often
export const OptimizedWidget = React.memo<WidgetProps>(({ data }) => {
  return <div>{/* Widget content */}</div>;
});

// Use useMemo for expensive calculations
const ExpensiveWidget: React.FC<Props> = ({ data }) => {
  const processedData = useMemo(() => {
    return expensiveCalculation(data);
  }, [data]);

  return <div>{processedData}</div>;
};
```

### 3. Type Safety

```tsx
// Define strict prop interfaces
interface StrictWidgetProps {
  title: string;
  data: Array<{ id: string; value: number }>;
  onUpdate: (id: string, value: number) => void;
}

// Use generic types for reusable widgets
interface GenericListWidgetProps<T> {
  items: T[];
  renderItem: (item: T) => React.ReactNode;
  onItemClick?: (item: T) => void;
}
```

## Troubleshooting

### Common Issues

1. **CSS not loading**: Ensure you've imported the required CSS files
2. **Drag and drop not working**: Check that `enableDrag` is set to `true`
3. **Widgets not rendering**: Verify that components are properly exported
4. **Layout issues**: Make sure grid layout props are correctly configured

### Debug Mode

Enable debug logging:

```tsx
// Add to your app initialization
if (process.env.NODE_ENV === 'development') {
  window.WIDGET_DEBUG = true;
}
```

### Getting Help

- Check the [API Reference](README.md#api-reference)
- Look at the [example widgets](src/example-widgets/)
- Run the demo application: `npm run dev`
- Open an issue on GitHub for bugs or feature requests

---

This guide should get you started with integrating the Widget Package into your application. For more advanced use cases, refer to the API documentation and example implementations.
