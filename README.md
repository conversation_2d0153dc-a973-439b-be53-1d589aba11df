# 🚀 Widget Package

A powerful, developer-friendly React TypeScript library for creating and managing widget catalogs with a provider-consumer pattern. Perfect for building dashboards, admin panels, and customizable user interfaces.

## ✨ Why Choose Widget Package?

- **🎯 Zero Configuration**: Works out of the box with sensible defaults
- **🔧 Highly Customizable**: Adapt to your design system and requirements
- **📱 Production Ready**: Built with TypeScript, tested, and optimized
- **🎨 Multiple Layouts**: Support for both grid and flexible tabbed layouts
- **🖱️ Intuitive UX**: Drag & drop functionality that users love
- **📚 Developer Friendly**: Comprehensive docs, examples, and TypeScript support

## 🌟 Key Features

- 🎯 **WidgetsProvider**: Context provider for widget management with built-in state
- 📋 **WidgetsCatalog**: Searchable and filterable widget catalog with drag support
- 🏗️ **Layout**: Grid and Flex layout components with drag & drop
- 🔍 **Search & Filter**: Built-in search and category filtering
- 🖱️ **Drag & Drop**: Full drag and drop support between catalog and layouts
- 📝 **TypeScript**: Full TypeScript support with type definitions
- 🎨 **Customizable**: Flexible widget rendering and styling
- 📐 **React Grid Layout**: Integration with react-grid-layout for advanced grid layouts
- 🔧 **FlexLayout**: Support for flexlayout-react with tabs, docking, and splitters
- ⚡ **Performance**: Optimized for large widget catalogs
- 🧪 **Testing**: Built-in testing utilities and examples

## 📦 Installation

### Quick Install (Recommended)

```bash
# npm
npm install widget-package react-grid-layout react-resizable flexlayout-react

# yarn
yarn add widget-package react-grid-layout react-resizable flexlayout-react

# pnpm
pnpm add widget-package react-grid-layout react-resizable flexlayout-react
```

### Peer Dependencies

The following packages are required peer dependencies:

| Package | Version | Purpose |
|---------|---------|---------|
| `react` | `>=18.0.0` | Core React library |
| `react-dom` | `>=18.0.0` | React DOM rendering |
| `react-grid-layout` | `>=1.3.0` | Grid layout functionality |
| `react-resizable` | `>=3.0.0` | Resizable components |
| `flexlayout-react` | `>=0.7.0` | Flexible tabbed layouts |

### CSS Imports

Don't forget to import the required CSS files in your main application file:

```tsx
// Required for grid layout
import 'react-grid-layout/css/styles.css';
import 'react-resizable/css/styles.css';

// Required for flex layout
import 'flexlayout-react/style/light.css';
// or for dark theme: import 'flexlayout-react/style/dark.css';
```

## 🚀 Quick Start

### 1. Create Your First Widget

```tsx
// MyWidget.tsx
import React from 'react';

interface MyWidgetProps {
  title?: string;
  count?: number;
}

export const MyWidget: React.FC<MyWidgetProps> = ({
  title = "My Widget",
  count = 0
}) => {
  return (
    <div style={{ padding: '16px', textAlign: 'center' }}>
      <h3>{title}</h3>
      <p>Count: {count}</p>
    </div>
  );
};
```

### 2. Basic Widget Catalog

```tsx
// App.tsx
import React from 'react';
import { WidgetsProvider, WidgetsCatalog } from 'widget-package';
import type { Widget } from 'widget-package';
import { MyWidget } from './MyWidget';

// Define your widgets
const widgets: Widget[] = [
  {
    id: 'my-widget',
    name: 'My Widget',
    description: 'A sample widget with customizable props',
    component: MyWidget,
    category: 'Demo',
    tags: ['example', 'demo'],
    props: { title: 'Hello World', count: 42 }
  }
];

function App() {
  return (
    <WidgetsProvider initialWidgets={widgets}>
      <div style={{ padding: '20px' }}>
        <h1>My Widget Catalog</h1>
        <WidgetsCatalog
          onWidgetSelect={(widget) => console.log('Selected:', widget)}
          showSearch={true}
          showCategories={true}
        />
      </div>
    </WidgetsProvider>
  );
}

export default App;
```

### 3. Complete Dashboard with Drag & Drop

```tsx
// Dashboard.tsx
import React, { useState } from 'react';
import { WidgetsProvider, WidgetsCatalog, Layout } from 'widget-package';
import type { Widget } from 'widget-package';
import type { Layout as RGLLayout } from 'react-grid-layout';
import { MyWidget } from './MyWidget';

// Import required CSS
import 'react-grid-layout/css/styles.css';
import 'react-resizable/css/styles.css';

const widgets: Widget[] = [
  {
    id: 'my-widget',
    name: 'My Widget',
    description: 'A sample widget',
    component: MyWidget,
    category: 'Demo',
    tags: ['example'],
    props: { title: 'Draggable Widget' },
    layout: { w: 4, h: 3, x: 0, y: 0 } // Default grid layout
  }
];

function Dashboard() {
  const [droppedWidgets, setDroppedWidgets] = useState<Widget[]>([]);
  const [currentLayout, setCurrentLayout] = useState<RGLLayout[]>([]);

  const handleDrop = (layout: RGLLayout[], item: RGLLayout, e: Event) => {
    const dragEvent = e as DragEvent;
    const dragData = dragEvent.dataTransfer?.getData('application/json');

    if (dragData) {
      const data = JSON.parse(dragData);
      const newWidget = {
        ...data.widget,
        id: `${data.widget.id}-${Date.now()}`, // Unique ID for each instance
        layout: { x: item.x, y: item.y, w: item.w, h: item.h }
      };
      setDroppedWidgets(prev => [...prev, newWidget]);
    }
  };

  const handleLayoutChange = (layout: RGLLayout[]) => {
    setCurrentLayout(layout);
  };

  return (
    <WidgetsProvider initialWidgets={widgets}>
      <div style={{ display: 'flex', height: '100vh' }}>
        {/* Widget Catalog Sidebar */}
        <div style={{ width: '300px', padding: '20px', borderRight: '1px solid #ddd' }}>
          <h2>Widget Catalog</h2>
          <WidgetsCatalog
            enableDrag={true}
            showSearch={true}
            showCategories={true}
            onDrag={(widget) => console.log('Dragging:', widget.name)}
          />
        </div>

        {/* Main Layout Area */}
        <div style={{ flex: 1, padding: '20px' }}>
          <h2>Dashboard Layout</h2>
          <Layout
            type="grid"
            onDrop={handleDrop}
            onLayoutChange={handleLayoutChange}
            layout={currentLayout}
            cols={12}
            rowHeight={60}
            isDraggable={true}
            isResizable={true}
            isDroppable={true}
          >
            {droppedWidgets.map((widget) => {
              const WidgetComponent = widget.component;
              return (
                <div key={widget.id} data-grid={widget.layout}>
                  <WidgetComponent {...(widget.props || {})} />
                </div>
              );
            })}
          </Layout>
        </div>
      </div>
    </WidgetsProvider>
  );
}

export default Dashboard;
```

## 🛠️ Utility Functions

The Widget Package includes powerful utility functions to simplify development:

### Widget Creation Helpers

```tsx
import { createWidget, createCategory } from 'widget-package';

// Create a widget with sensible defaults
const myWidget = createWidget(
  'my-widget-id',
  'My Widget Name',
  MyWidgetComponent,
  {
    description: 'Widget description',
    category: 'Custom',
    tags: ['tag1', 'tag2'],
    props: { prop1: 'value1' },
    layout: { w: 4, h: 3 }
  }
);

// Create a category
const myCategory = createCategory(
  'my-category',
  'My Category',
  [widget1, widget2],
  'Category description'
);
```

### Layout Utilities

```tsx
import {
  findOptimalPosition,
  widgetToGridLayout,
  createDefaultGridConfig
} from 'widget-package';

// Find optimal position for a new widget
const position = findOptimalPosition(existingLayouts, newWidget);

// Convert widget to grid layout format
const gridLayout = widgetToGridLayout(widget);

// Get default grid configuration
const gridConfig = createDefaultGridConfig();
```

### Widget Registry for Serialization

```tsx
import { WidgetRegistry, serializeWidget, deserializeWidget } from 'widget-package';

// Create a registry for drag & drop
const registry = new WidgetRegistry();
registry.register('my-widget', MyWidgetComponent);

// Serialize widget for drag & drop
const serialized = serializeWidget(widget);

// Deserialize and restore component
const restored = deserializeWidget(serialized, registry);
```

## 🧪 Testing Utilities

Built-in testing utilities help you test widgets effectively:

```tsx
import {
  renderWithWidgetsProvider,
  createMockWidget,
  WidgetTestHarness,
  testUtils
} from 'widget-package';

// Render widget with provider context
const { getByTestId } = renderWithWidgetsProvider(
  <MyWidget />,
  { providerProps: { initialWidgets: [mockWidget] } }
);

// Create mock widgets for testing
const mockWidget = createMockWidget({
  id: 'test-widget',
  name: 'Test Widget',
  component: MyWidget
});

// Test widget in isolation
<WidgetTestHarness
  widget={myWidget}
  props={{ testProp: 'value' }}
/>

// Validate widget configuration
const { isValid, errors } = testUtils.validateWidget(widget);
```

## 📚 Documentation & Examples

### 📖 Complete Documentation

Comprehensive guides for developers:

- **[API Reference](docs/API.md)** - Complete API documentation with examples
- **[Component Guide](docs/COMPONENTS.md)** - Detailed component usage and patterns
- **[Hooks Documentation](docs/HOOKS.md)** - Hook usage and custom hook patterns
- **[Utilities Reference](docs/UTILITIES.md)** - Utility functions and helper tools
- **[Integration Guide](INTEGRATION_GUIDE.md)** - Step-by-step integration instructions

### 🎯 Examples & Demos

Explore our comprehensive examples:

- **[Basic Integration](examples/basic/)** - Simple setup and widget creation
- **[Dashboard Application](examples/dashboard/)** - Full dashboard with drag & drop
- **[Analytics Dashboard](examples/analytics/)** - Data visualization widgets
- **[Admin Panel](examples/admin-panel/)** - Advanced features and permissions
- **[CMS Integration](examples/cms/)** - Content management system

### 🛠️ Development Resources

- **[Development Roadmap](plan/README.md)** - Future features and improvements
- **[Task Management](tasks/README.md)** - Current development tasks
- **[Change Log](LOG.md)** - Version history and updates

## 📚 API Reference

> **📖 For complete API documentation, see [docs/API.md](docs/API.md)**

### WidgetsProvider

The main context provider that manages widget state and provides context to child components.

```tsx
interface WidgetsProviderProps {
  children: React.ReactNode;
  initialWidgets?: Widget[];           // Pre-populate with widgets
  initialCategories?: WidgetCategory[]; // Pre-populate with categories
}
```

**Example:**
```tsx
<WidgetsProvider initialWidgets={myWidgets}>
  <MyApp />
</WidgetsProvider>
```

### WidgetsCatalog

A component that displays widgets in a searchable, filterable catalog with drag support.

```tsx
interface WidgetsCatalogProps {
  className?: string;                                    // Custom CSS class
  showSearch?: boolean;                                  // Show search input
  showCategories?: boolean;                              // Show category filter
  widgets?: Widget[];                                    // Override context widgets
  onWidgetSelect?: (widget: Widget) => void;            // Widget selection handler
  onDrag?: (widget: Widget) => void;                    // Drag start handler
  renderWidget?: (widget: Widget) => React.ReactNode;   // Custom widget renderer
  enableDrag?: boolean;                                  // Enable drag functionality
  renderSearch?: (props: SearchProps) => React.ReactNode;     // Custom search input
  renderCategorySelect?: (props: CategoryProps) => React.ReactNode; // Custom category select
}
```

**Example:**
```tsx
<WidgetsCatalog
  enableDrag={true}
  showSearch={true}
  showCategories={true}
  onWidgetSelect={(widget) => console.log('Selected:', widget.name)}
  renderWidget={(widget) => (
    <CustomWidgetCard widget={widget} />
  )}
/>
```

### Layout

A component that provides both grid and flex layout with drag & drop support.

#### Grid Layout (react-grid-layout)

Uses react-grid-layout for resizable, draggable grid-based layouts.

#### Flex Layout (flexlayout-react)

Uses flexlayout-react for tabbed, dockable layouts with splitters and advanced layout management.

```tsx
interface LayoutProps {
  type: 'grid' | 'flex';
  children?: React.ReactNode;
  className?: string;

  // Grid layout specific props (react-grid-layout)
  onDrop?: (layout: Layout[], item: Layout, e: Event) => void;
  onLayoutChange?: (layout: Layout[]) => void;
  layout?: Layout[];
  cols?: number;
  rowHeight?: number;
  isDraggable?: boolean;
  isResizable?: boolean;
  isDroppable?: boolean;

  // Flex layout specific props (flexlayout-react)
  flexModel?: Model | null; // FlexLayout Model
  flexFactory?: (node: TabNode) => React.ReactNode; // Factory function
  onFlexModelChange?: (model: Model) => void;

  // Simple flex layout props (CSS flexbox fallback)
  flexDirection?: 'row' | 'column' | 'row-reverse' | 'column-reverse';
  justifyContent?: 'flex-start' | 'flex-end' | 'center' | 'space-between' | 'space-around' | 'space-evenly';
  alignItems?: 'stretch' | 'flex-start' | 'flex-end' | 'center' | 'baseline';
  flexWrap?: 'nowrap' | 'wrap' | 'wrap-reverse';
  gap?: number | string;
}
```

#### FlexLayout Usage Example

```tsx
import { Model, TabNode } from 'flexlayout-react';
import 'flexlayout-react/style/light.css';

// Create FlexLayout model
const flexModel = Model.fromJson({
  global: {
    tabEnableClose: true,
    tabSetEnableDrop: true,
    tabSetEnableDrag: true,
  },
  layout: {
    type: "row",
    weight: 100,
    children: [{
      type: "tabset",
      weight: 100,
      children: []
    }]
  }
});

// Factory function to render tab content
const flexFactory = (node: TabNode) => {
  const component = node.getComponent();
  if (component === 'widget') {
    const widgetId = node.getConfig().widgetId;
    return <MyWidget id={widgetId} />;
  }
  return <div>Unknown component</div>;
};

// Use in Layout component
<Layout
  type="flex"
  flexModel={flexModel}
  flexFactory={flexFactory}
  onFlexModelChange={setFlexModel}
/>
```

### useWidgets Hook

Access the widgets context from any component within the provider.

```tsx
const {
  widgets,
  categories,
  addWidget,
  removeWidget,
  getWidgetsByCategory,
  searchWidgets
} = useWidgets();
```

### Widget Interface

```tsx
interface Widget {
  id: string;
  name: string;
  description?: string;
  component: React.ComponentType<any>;
  props?: Record<string, any>;
  category?: string;
  tags?: string[];
  // Layout properties for grid layout
  layout?: {
    x?: number;
    y?: number;
    w?: number;
    h?: number;
    minW?: number;
    minH?: number;
    maxW?: number;
    maxH?: number;
    static?: boolean;
    isDraggable?: boolean;
    isResizable?: boolean;
  };
}
```

## Development

### Running the Demo

To run the interactive demo locally:

```bash
npm install
npm run dev
```

The demo includes:

- **Pre-populated widgets** to showcase functionality
- **Interactive controls** for switching between layout types
- **Developer tools** like "Clear Layout" and "Add Demo Widgets" buttons
- **Real-time stats** showing widget counts and layout information
- **Comprehensive examples** of both Grid and Flex layouts

### Building the Library

To build the library for distribution:

```bash
npm run build:lib
```

### Demo Features for Developers

The demo application is designed to help developers understand and test the widget package:

#### **Grid Layout Testing**

- Larger default widget sizes (4x3 grid units) for better visibility
- Increased layout area (800px height) for extensive testing
- Pre-populated demo widgets showing real functionality
- Interactive resize and drag capabilities
- 12-column responsive grid system

#### **Flex Layout Testing**

- Welcome tab with usage instructions
- Drag and drop from catalog to create tabs
- Tab management (reorder, dock, split, maximize)
- Professional IDE-like interface

#### **Developer Tools**

- **Layout Type Switcher**: Easy switching between Grid and Flex layouts
- **Clear Layout Button**: Reset to empty state for fresh testing
- **Add Demo Widgets Button**: Quickly populate with example widgets
- **Real-time Statistics**: Track widget counts and layout state
- **Interactive Instructions**: Context-sensitive help for each layout type

## License

MIT

## Layout Component

The `Layout` component provides a fully customizable layout engine for both grid (react-grid-layout) and flex (flexlayout-react or CSS flexbox) layouts. It is designed for maximum developer flexibility and robustness.

### Features
- **Grid and Flex Layouts:** Switch between grid and flex modes with the `type` prop.
- **Full Prop Customization:** All layout, style, and behavior props are exposed for both grid and flex modes.
- **Custom Drop Behavior:** Use `droppingItem` (grid) and `droppingItemFlex` (flex) to control default dropped widget properties.
- **Custom Class Names and Styles:** Use `layoutClassName` and `layoutStyle` for both grid and flex containers.
- **Error Boundaries:** Each widget is wrapped in an error boundary to prevent a single widget from breaking the layout.
- **Empty State:** Friendly message shown when no widgets are present.
- **Type Safety:** All required types are enforced for robust usage.

### Example Usage
```tsx
<Layout
  type="grid"
  layout={currentLayout}
  onDrop={handleDrop}
  onLayoutChange={handleLayoutChange}
  cols={12}
  rowHeight={50}
  margin={[16, 16]}
  containerPadding={[24, 24]}
  isDraggable
  isResizable
  isDroppable
  droppingItem={{ w: 6, h: 4 }} // Custom default drop size for grid
  layoutClassName="my-grid"
  layoutStyle={{ background: '#fafbfc' }}
>
  {widgets.map(widget => (
    <WidgetErrorBoundary key={widget.id} widgetName={widget.name}>
      <WidgetComponent {...widget.props} />
    </WidgetErrorBoundary>
  ))}
</Layout>

<Layout
  type="flex"
  flexModel={flexModel}
  flexFactory={flexFactory}
  onFlexModelChange={setFlexModel}
  droppingItemFlex={{ w: 3, h: 2 }} // Custom default drop size for flex
  layoutClassName="my-flex"
  layoutStyle={{ minHeight: 400 }}
>
  {/* Fallback flex children */}
</Layout>
```

### Key Props
- `type`: 'grid' | 'flex' — Selects the layout engine.
- `layout`, `onDrop`, `onLayoutChange`, `cols`, `rowHeight`, etc.: Grid-specific props.
- `flexModel`, `flexFactory`, `onFlexModelChange`: FlexLayout-specific props.
- `droppingItem`, `droppingItemFlex`: Control default dropped widget properties for grid/flex.
- `layoutClassName`, `layoutStyle`: Custom className and style for the layout container.
- All other props are passed to the outer container for further customization.

### Error Handling
Each widget is wrapped in a `WidgetErrorBoundary` to prevent a single widget error from breaking the entire layout.

### Empty State
When no widgets are present, a friendly message is shown to guide the user.

---

For more advanced usage, see the integration guide and example projects.
