{"name": "widget-package", "version": "1.0.0", "type": "module", "main": "dist/index.js", "module": "dist/index.js", "types": "dist/index.d.ts", "files": ["dist"], "exports": {".": {"import": "./dist/index.js", "types": "./dist/index.d.ts"}}, "scripts": {"dev": "vite", "build": " tsc && vite build", "build:lib": " tsc -p tsconfig.lib.json", "lint": "eslint .", "preview": "vite preview", "prepublishOnly": "npm run build:lib"}, "peerDependencies": {"flexlayout-react": ">=0.7.0", "react": ">=18.0.0", "react-dom": ">=18.0.0", "react-grid-layout": ">=1.3.0", "react-resizable": ">=3.0.0"}, "dependencies": {}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@types/react-grid-layout": "^1.3.5", "@vitejs/plugin-react": "^4.4.1", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "flexlayout-react": "^0.8.17", "globals": "^16.0.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-grid-layout": "^1.5.1", "react-resizable": "^3.0.5", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5"}}