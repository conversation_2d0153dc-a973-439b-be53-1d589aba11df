# Responsive Widgets Guide

This guide explains how to create responsive widgets that automatically adapt to different grid item sizes in the widget system.

## Overview

The widget system now includes responsive CSS classes and container queries that make widgets automatically scale their content based on the size of the grid item they're placed in. When you resize a grid item, the widget content will automatically adjust its font sizes, padding, spacing, and layout.

## Responsive CSS Classes

### Core Container Class

**`.widget-content`** - The main container class for responsive widgets
- Sets up flexbox layout with responsive gaps
- Provides base responsive font sizing
- Should be the root element of your widget

```jsx
<div className="widget-content">
  {/* Your widget content */}
</div>
```

### Content Classes

**`.widget-display-value`** - For prominent display values (counters, status, etc.)
- Large, responsive font size
- Centered content with flexible padding
- Ideal for showing main widget values

**`.widget-button-group`** - For button containers
- Responsive gap between buttons
- Flex layout with wrapping
- Centered alignment

**`.widget-input-group`** - For input field containers
- Responsive spacing between inputs and labels
- Flexible layout that adapts to container width

**`.widget-list-item`** - For list items (todos, notifications, etc.)
- Responsive padding and font sizes
- Consistent spacing between items

## Container Queries

The system uses CSS container queries to provide different styling based on the widget's container size:

### Width-based Breakpoints
- **< 200px**: Extra small (minimal padding, small fonts)
- **200px - 300px**: Small (compact layout)
- **> 300px**: Large (full spacing and fonts)

### Height-based Breakpoints
- **< 150px**: Very short (minimal gaps, compact elements)
- **150px - 250px**: Medium height (balanced spacing)
- **> 250px**: Tall (full spacing)

## Example: Responsive Counter Widget

```jsx
import React, { useState } from "react";

export const ResponsiveCounterWidget = ({ initialValue = 0, step = 1 }) => {
  const [count, setCount] = useState(initialValue);

  return (
    <div className="widget-content" style={{ textAlign: "center", backgroundColor: "#f8f9fa" }}>
      <h3>Counter Widget</h3>
      <div
        className="widget-display-value"
        style={{
          color: "#fff",
          border: "3px solid #007bff",
          backgroundColor: "#007bff",
        }}
      >
        COUNT: {count}
      </div>
      <div className="widget-button-group">
        <button
          onClick={() => setCount(count - step)}
          style={{ backgroundColor: "#dc3545", color: "white" }}
        >
          -
        </button>
        <button
          onClick={() => setCount(count + step)}
          style={{ backgroundColor: "#28a745", color: "white" }}
        >
          +
        </button>
      </div>
    </div>
  );
};
```

## Best Practices

### 1. Use Semantic HTML Structure
```jsx
<div className="widget-content">
  <h3>Widget Title</h3>
  <div className="widget-display-value">Main Content</div>
  <div className="widget-button-group">
    <button>Action 1</button>
    <button>Action 2</button>
  </div>
</div>
```

### 2. Flexible Input Groups
```jsx
<div className="widget-input-group">
  <input style={{ flex: 1, minWidth: "120px" }} />
  <button style={{ flexShrink: 0 }}>Submit</button>
</div>
```

### 3. Scrollable Content Areas
```jsx
<div className="widget-content">
  <h3>Title</h3>
  <div style={{ flex: 1, overflow: "auto" }}>
    {/* Scrollable content */}
  </div>
</div>
```

### 4. Responsive List Items
```jsx
{items.map(item => (
  <div key={item.id} className="widget-list-item">
    <span style={{ flex: 1, wordBreak: "break-word" }}>
      {item.text}
    </span>
    <button style={{ flexShrink: 0 }}>
      Action
    </button>
  </div>
))}
```

## Key Features

✅ **Automatic scaling** - Content scales with grid item size
✅ **Container queries** - Responsive breakpoints based on widget size, not viewport
✅ **Flexible layouts** - Flexbox-based responsive layouts
✅ **Consistent spacing** - Responsive gaps and padding
✅ **Readable text** - Font sizes that scale appropriately
✅ **Touch-friendly** - Button sizes adapt for different screen sizes
✅ **Overflow handling** - Proper scrolling for content that doesn't fit

## Migration from Fixed Widgets

To convert existing widgets to be responsive:

1. Replace the root container with `className="widget-content"`
2. Use responsive utility classes for common elements
3. Replace fixed padding/margins with flex gaps
4. Use `flex: 1` for expandable content areas
5. Add `flexShrink: 0` for fixed-size elements
6. Use `wordBreak: "break-word"` for text that might overflow

The responsive system ensures your widgets look great at any size!
